# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Set up plotting style
plt.style.use('default')
sns.set_palette('husl')

# Load football database files
import os
import glob

# Define the data directory path
data_dir = '/kaggle/input/football-database'

# Alternative local path if running locally
if not os.path.exists(data_dir):
    data_dir = './football-database'  # Local directory
    if not os.path.exists(data_dir):
        data_dir = '.'  # Current directory

print(f"Looking for data in: {data_dir}")

# Specific football data files in your workspace
football_files = [
    'players.csv',
    'teams.csv', 
    'leagues.csv',
    'games.csv',
    'appearances.csv',
    'shots.csv',
    'teamstats.csv'
]

# Find all CSV files in the data directory
csv_files = glob.glob(os.path.join(data_dir, '*.csv'))
print(f"Found {len(csv_files)} CSV files:")
for file in csv_files:
    print(f"  - {os.path.basename(file)}")

# Load all football database tables
dataframes = {}

# Load the specific football files
for filename in football_files:
    filepath = os.path.join(data_dir, filename)
    if os.path.exists(filepath):
        try:
            df_name = filename.replace('.csv', '')
            dataframes[df_name] = pd.read_csv(filepath)
            print(f"✓ Loaded {filename}: {dataframes[df_name].shape}")
        except Exception as e:
            print(f"✗ Error loading {filename}: {e}")
    else:
        print(f"✗ File not found: {filename}")

# Load any additional CSV files found
for csv_file in csv_files:
    filename = os.path.basename(csv_file)
    df_name = filename.replace('.csv', '')
    
    if df_name not in dataframes:
        try:
            dataframes[df_name] = pd.read_csv(csv_file)
            print(f"✓ Loaded additional file {filename}: {dataframes[df_name].shape}")
        except Exception as e:
            print(f"✗ Error loading {filename}: {e}")

print(f"\nTotal datasets loaded: {len(dataframes)}")
print("Available dataframes:", list(dataframes.keys()))

# Show file sizes and basic info
print("\n" + "="*60)
print("DATASET OVERVIEW")
print("="*60)
for name, df in dataframes.items():
    print(f"{name:15} | Rows: {df.shape[0]:>8,} | Columns: {df.shape[1]:>3} | Size: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

# Detailed exploration of each dataset
for name, df in dataframes.items():
    print(f"\n{'='*60}")
    print(f"📊 DATASET: {name.upper()}")
    print(f"{'='*60}")
    print(f"Shape: {df.shape[0]:,} rows × {df.shape[1]} columns")
    print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
    print(f"\n📋 Columns ({len(df.columns)}): {', '.join(df.columns)}")
    
    # Data types
    print(f"\n🔢 Data Types:")
    for col, dtype in df.dtypes.items():
        print(f"  {col}: {dtype}")
    
    # Missing values
    missing = df.isnull().sum()
    if missing.sum() > 0:
        print(f"\n❌ Missing Values:")
        for col, count in missing[missing > 0].items():
            percentage = (count / len(df)) * 100
            print(f"  {col}: {count:,} ({percentage:.1f}%)")
    else:
        print(f"\n✅ No missing values")
    
    # Sample data
    print(f"\n📋 Sample Data (first 3 rows):")
    display(df.head(3))
    
    # Basic statistics for numeric columns
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        print(f"\n📊 Numeric Summary:")
        display(df[numeric_cols].describe())
    
    print("\n" + "-"*60)

# Dataset relationships and key connections
print("\n🔗 DATASET RELATIONSHIPS")
print("="*50)

# Check for common ID columns to understand relationships
id_columns = {}
for name, df in dataframes.items():
    ids = [col for col in df.columns if 'ID' in col or 'Id' in col]
    if ids:
        id_columns[name] = ids
        print(f"{name:15}: {', '.join(ids)}")

# Show unique counts for key ID columns
print("\n📊 Unique ID Counts:")
for name, df in dataframes.items():
    for col in df.columns:
        if 'ID' in col and col in ['playerID', 'teamID', 'gameID', 'leagueID']:
            unique_count = df[col].nunique()
            print(f"{name:15} - {col:10}: {unique_count:,} unique values")

# Comprehensive Football Data Analysis

print("🏈 FOOTBALL DATABASE ANALYSIS")
print("="*50)

# 1. PLAYERS ANALYSIS
if 'players' in dataframes:
    players_df = dataframes['players']
    print(f"\n📊 PLAYERS ANALYSIS")
    print(f"Total unique players: {len(players_df):,}")
    print(f"Columns: {list(players_df.columns)}")
    print(f"Sample players: {players_df['name'].head().tolist()}")

# 2. TEAMS ANALYSIS  
if 'teams' in dataframes:
    teams_df = dataframes['teams']
    print(f"\n⚽ TEAMS ANALYSIS")
    print(f"Total teams: {len(teams_df):,}")
    print(f"Columns: {list(teams_df.columns)}")
    print(f"Sample teams: {teams_df['name'].head().tolist()}")

# 3. LEAGUES ANALYSIS
if 'leagues' in dataframes:
    leagues_df = dataframes['leagues']
    print(f"\n🏆 LEAGUES ANALYSIS")
    print(f"Total leagues: {len(leagues_df):,}")
    print(f"Columns: {list(leagues_df.columns)}")
    print("Available leagues:")
    for _, league in leagues_df.iterrows():
        print(f"  - {league['name']} ({league['understatNotation']})")

# 4. GAMES ANALYSIS
if 'games' in dataframes:
    games_df = dataframes['games']
    print(f"\n🎮 GAMES ANALYSIS")
    print(f"Total games: {len(games_df):,}")
    print(f"Columns: {len(games_df.columns)} - {list(games_df.columns[:10])}...")
    
    # Convert date column
    games_df['date'] = pd.to_datetime(games_df['date'])
    
    # Basic game statistics
    print(f"Date range: {games_df['date'].min().date()} to {games_df['date'].max().date()}")
    print(f"Seasons covered: {sorted(games_df['season'].unique())}")
    print(f"Total goals (home): {games_df['homeGoals'].sum():,}")
    print(f"Total goals (away): {games_df['awayGoals'].sum():,}")
    print(f"Average goals per game: {(games_df['homeGoals'] + games_df['awayGoals']).mean():.2f}")
    
    # League distribution
    if 'leagues' in dataframes:
        league_games = games_df.groupby('leagueID').size().reset_index(name='game_count')
        league_games = league_games.merge(leagues_df, left_on='leagueID', right_on='leagueID')
        print("\nGames per league:")
        for _, row in league_games.iterrows():
            print(f"  - {row['name']}: {row['game_count']:,} games")

# 5. APPEARANCES ANALYSIS
if 'appearances' in dataframes:
    appearances_df = dataframes['appearances']
    print(f"\n👤 PLAYER APPEARANCES ANALYSIS")
    print(f"Total appearances: {len(appearances_df):,}")
    print(f"Columns: {len(appearances_df.columns)} - {list(appearances_df.columns[:10])}...")
    
    # Goals and assists
    total_goals = appearances_df['goals'].sum()
    total_assists = appearances_df['assists'].sum()
    total_shots = appearances_df['shots'].sum()
    
    print(f"Total goals scored: {total_goals:,}")
    print(f"Total assists: {total_assists:,}")
    print(f"Total shots: {total_shots:,}")
    print(f"Average xGoals per appearance: {appearances_df['xGoals'].mean():.3f}")
    
    # Position analysis
    position_counts = appearances_df['position'].value_counts()
    print(f"\nMost common positions:")
    for pos, count in position_counts.head().items():
        print(f"  - {pos}: {count:,} appearances")

# 6. SHOTS ANALYSIS
if 'shots' in dataframes:
    shots_df = dataframes['shots']
    print(f"\n🎯 SHOTS ANALYSIS")
    print(f"Total shots: {len(shots_df):,}")
    print(f"Columns: {list(shots_df.columns)}")
    
    # Shot outcomes
    shot_results = shots_df['shotResult'].value_counts()
    print("\nShot outcomes:")
    for result, count in shot_results.items():
        percentage = (count / len(shots_df)) * 100
        print(f"  - {result}: {count:,} ({percentage:.1f}%)")
    
    # Shot types
    shot_types = shots_df['shotType'].value_counts()
    print("\nShot types:")
    for shot_type, count in shot_types.head().items():
        print(f"  - {shot_type}: {count:,}")
    
    # Average xGoal
    print(f"\nAverage xGoal per shot: {shots_df['xGoal'].mean():.3f}")

# 7. TEAM STATS ANALYSIS
if 'teamstats' in dataframes:
    teamstats_df = dataframes['teamstats']
    print(f"\n📈 TEAM STATISTICS ANALYSIS")
    print(f"Total team game records: {len(teamstats_df):,}")
    print(f"Columns: {list(teamstats_df.columns)}")
    
    # Convert date
    teamstats_df['date'] = pd.to_datetime(teamstats_df['date'])
    
    # Home vs Away performance
    home_stats = teamstats_df[teamstats_df['location'] == 'h']
    away_stats = teamstats_df[teamstats_df['location'] == 'a']
    
    print(f"\nHome vs Away comparison:")
    print(f"Home games: {len(home_stats):,}")
    print(f"Away games: {len(away_stats):,}")
    print(f"Average goals (home): {home_stats['goals'].mean():.2f}")
    print(f"Average goals (away): {away_stats['goals'].mean():.2f}")
    print(f"Average xGoals (home): {home_stats['xGoals'].mean():.2f}")
    print(f"Average xGoals (away): {away_stats['xGoals'].mean():.2f}")
    
    # Results distribution
    results = teamstats_df['result'].value_counts()
    print(f"\nResults distribution:")
    for result, count in results.items():
        percentage = (count / len(teamstats_df)) * 100
        print(f"  - {result}: {count:,} ({percentage:.1f}%)")

# 🎨 COMPREHENSIVE FOOTBALL DATA VISUALIZATIONS
print("Creating visualizations for football data...")

# 1. GAMES ANALYSIS VISUALIZATIONS
if 'games' in dataframes:
    games_df = dataframes['games'].copy()
    games_df['date'] = pd.to_datetime(games_df['date'])
    games_df['total_goals'] = games_df['homeGoals'] + games_df['awayGoals']
    
    # Create comprehensive games analysis
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('⚽ FOOTBALL GAMES ANALYSIS', fontsize=16, fontweight='bold')
    
    # 1.1 Goals distribution
    axes[0,0].hist(games_df['total_goals'], bins=15, alpha=0.7, edgecolor='black', color='skyblue')
    axes[0,0].set_title('Total Goals per Game Distribution')
    axes[0,0].set_xlabel('Total Goals')
    axes[0,0].set_ylabel('Number of Games')
    axes[0,0].grid(True, alpha=0.3)
    
    # 1.2 Home vs Away goals scatter
    axes[0,1].scatter(games_df['homeGoals'], games_df['awayGoals'], alpha=0.6, color='coral')
    axes[0,1].set_title('Home vs Away Goals')
    axes[0,1].set_xlabel('Home Goals')
    axes[0,1].set_ylabel('Away Goals')
    axes[0,1].grid(True, alpha=0.3)
    
    # 1.3 Home advantage analysis
    home_away_data = [games_df['homeGoals'], games_df['awayGoals']]
    bp = axes[0,2].boxplot(home_away_data, labels=['Home', 'Away'], patch_artist=True)
    bp['boxes'][0].set_facecolor('lightgreen')
    bp['boxes'][1].set_facecolor('lightcoral')
    axes[0,2].set_title('Home vs Away Goals Distribution')
    axes[0,2].set_ylabel('Goals')
    axes[0,2].grid(True, alpha=0.3)
    
    # 1.4 Goals over time
    monthly_goals = games_df.groupby(games_df['date'].dt.to_period('M'))['total_goals'].mean()
    monthly_goals.plot(ax=axes[1,0], color='purple', linewidth=2)
    axes[1,0].set_title('Average Goals per Game Over Time')
    axes[1,0].set_xlabel('Date')
    axes[1,0].set_ylabel('Average Goals')
    axes[1,0].grid(True, alpha=0.3)
    axes[1,0].tick_params(axis='x', rotation=45)
    
    # 1.5 League distribution (if leagues data available)
    if 'leagues' in dataframes:
        league_games = games_df.groupby('leagueID').size().reset_index(name='count')
        league_games = league_games.merge(dataframes['leagues'], on='leagueID')
        axes[1,1].bar(league_games['name'], league_games['count'], color='gold')
        axes[1,1].set_title('Games per League')
        axes[1,1].set_ylabel('Number of Games')
        axes[1,1].tick_params(axis='x', rotation=45)
        axes[1,1].grid(True, alpha=0.3)
    
    # 1.6 Season comparison
    season_stats = games_df.groupby('season').agg({
        'total_goals': 'mean',
        'gameID': 'count'
    }).reset_index()
    
    ax2 = axes[1,2].twinx()
    bars = axes[1,2].bar(season_stats['season'], season_stats['gameID'], alpha=0.7, color='lightblue', label='Games')
    line = ax2.plot(season_stats['season'], season_stats['total_goals'], color='red', marker='o', linewidth=2, label='Avg Goals')
    
    axes[1,2].set_title('Games and Average Goals by Season')
    axes[1,2].set_xlabel('Season')
    axes[1,2].set_ylabel('Number of Games', color='blue')
    ax2.set_ylabel('Average Goals per Game', color='red')
    axes[1,2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# 2. PLAYER PERFORMANCE VISUALIZATIONS
if 'appearances' in dataframes:
    appearances_df = dataframes['appearances'].copy()
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('👤 PLAYER PERFORMANCE ANALYSIS', fontsize=16, fontweight='bold')
    
    # 2.1 Goals distribution
    axes[0,0].hist(appearances_df['goals'], bins=20, alpha=0.7, edgecolor='black', color='green')
    axes[0,0].set_title('Goals per Appearance Distribution')
    axes[0,0].set_xlabel('Goals')
    axes[0,0].set_ylabel('Number of Appearances')
    axes[0,0].set_yscale('log')
    axes[0,0].grid(True, alpha=0.3)
    
    # 2.2 xGoals vs Actual Goals
    sample_data = appearances_df[appearances_df['goals'] <= 5].sample(min(5000, len(appearances_df)))
    axes[0,1].scatter(sample_data['xGoals'], sample_data['goals'], alpha=0.6, color='orange')
    axes[0,1].plot([0, sample_data['xGoals'].max()], [0, sample_data['xGoals'].max()], 'r--', label='Perfect prediction')
    axes[0,1].set_title('Expected vs Actual Goals')
    axes[0,1].set_xlabel('xGoals')
    axes[0,1].set_ylabel('Actual Goals')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 2.3 Position distribution
    position_counts = appearances_df['position'].value_counts().head(10)
    axes[0,2].bar(position_counts.index, position_counts.values, color='purple')
    axes[0,2].set_title('Top 10 Positions by Appearances')
    axes[0,2].set_ylabel('Number of Appearances')
    axes[0,2].tick_params(axis='x', rotation=45)
    axes[0,2].grid(True, alpha=0.3)
    
    # 2.4 Shots vs Goals relationship
    shot_goal_data = appearances_df[appearances_df['shots'] <= 10].groupby('shots')['goals'].mean()
    axes[1,0].plot(shot_goal_data.index, shot_goal_data.values, marker='o', color='red', linewidth=2)
    axes[1,0].set_title('Average Goals by Shots Taken')
    axes[1,0].set_xlabel('Shots per Appearance')
    axes[1,0].set_ylabel('Average Goals')
    axes[1,0].grid(True, alpha=0.3)
    
    # 2.5 Cards distribution
    card_data = {
        'Yellow Cards': appearances_df['yellowCard'].sum(),
        'Red Cards': appearances_df['redCard'].sum(),
        'No Cards': len(appearances_df) - appearances_df['yellowCard'].sum() - appearances_df['redCard'].sum()
    }
    axes[1,1].pie(card_data.values(), labels=card_data.keys(), autopct='%1.1f%%', colors=['yellow', 'red', 'lightgray'])
    axes[1,1].set_title('Card Distribution')
    
    # 2.6 Playing time distribution
    axes[1,2].hist(appearances_df['time'], bins=30, alpha=0.7, edgecolor='black', color='cyan')
    axes[1,2].set_title('Playing Time Distribution')
    axes[1,2].set_xlabel('Minutes Played')
    axes[1,2].set_ylabel('Number of Appearances')
    axes[1,2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# 3. SHOTS ANALYSIS VISUALIZATIONS
if 'shots' in dataframes:
    shots_df = dataframes['shots'].copy()
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('🎯 SHOTS ANALYSIS', fontsize=16, fontweight='bold')
    
    # 3.1 Shot outcomes
    shot_results = shots_df['shotResult'].value_counts()
    colors = ['gold', 'lightcoral', 'lightblue', 'lightgreen']
    axes[0,0].pie(shot_results.values, labels=shot_results.index, autopct='%1.1f%%', colors=colors[:len(shot_results)])
    axes[0,0].set_title('Shot Outcomes Distribution')
    
    # 3.2 Shot types
    shot_types = shots_df['shotType'].value_counts().head(8)
    axes[0,1].bar(shot_types.index, shot_types.values, color='orange')
    axes[0,1].set_title('Shot Types')
    axes[0,1].set_ylabel('Number of Shots')
    axes[0,1].tick_params(axis='x', rotation=45)
    axes[0,1].grid(True, alpha=0.3)
    
    # 3.3 xGoal distribution
    axes[0,2].hist(shots_df['xGoal'], bins=30, alpha=0.7, edgecolor='black', color='purple')
    axes[0,2].set_title('xGoal Distribution')
    axes[0,2].set_xlabel('xGoal Value')
    axes[0,2].set_ylabel('Number of Shots')
    axes[0,2].grid(True, alpha=0.3)
    
    # 3.4 Shot positions heatmap
    axes[1,0].hexbin(shots_df['positionX'], shots_df['positionY'], gridsize=20, cmap='Reds')
    axes[1,0].set_title('Shot Position Heatmap')
    axes[1,0].set_xlabel('Position X')
    axes[1,0].set_ylabel('Position Y')
    
    # 3.5 Situation analysis
    situations = shots_df['situation'].value_counts().head(6)
    axes[1,1].bar(situations.index, situations.values, color='green')
    axes[1,1].set_title('Shot Situations')
    axes[1,1].set_ylabel('Number of Shots')
    axes[1,1].tick_params(axis='x', rotation=45)
    axes[1,1].grid(True, alpha=0.3)
    
    # 3.6 xGoal by shot type
    xgoal_by_type = shots_df.groupby('shotType')['xGoal'].mean().sort_values(ascending=False).head(8)
    axes[1,2].bar(xgoal_by_type.index, xgoal_by_type.values, color='red')
    axes[1,2].set_title('Average xGoal by Shot Type')
    axes[1,2].set_ylabel('Average xGoal')
    axes[1,2].tick_params(axis='x', rotation=45)
    axes[1,2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# 4. TEAM PERFORMANCE VISUALIZATIONS
if 'teamstats' in dataframes:
    teamstats_df = dataframes['teamstats'].copy()
    teamstats_df['date'] = pd.to_datetime(teamstats_df['date'])
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('📈 TEAM PERFORMANCE ANALYSIS', fontsize=16, fontweight='bold')
    
    # 4.1 Home vs Away performance
    home_away_goals = teamstats_df.groupby('location')['goals'].mean()
    colors = ['lightgreen', 'lightcoral']
    axes[0,0].bar(['Home', 'Away'], home_away_goals.values, color=colors)
    axes[0,0].set_title('Average Goals: Home vs Away')
    axes[0,0].set_ylabel('Average Goals')
    axes[0,0].grid(True, alpha=0.3)
    
    # 4.2 Goals vs xGoals
    sample_team_data = teamstats_df.sample(min(2000, len(teamstats_df)))
    axes[0,1].scatter(sample_team_data['xGoals'], sample_team_data['goals'], alpha=0.6, color='blue')
    axes[0,1].plot([0, sample_team_data['xGoals'].max()], [0, sample_team_data['xGoals'].max()], 'r--', label='Perfect prediction')
    axes[0,1].set_title('Expected vs Actual Goals (Team Level)')
    axes[0,1].set_xlabel('xGoals')
    axes[0,1].set_ylabel('Actual Goals')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 4.3 Results distribution
    results = teamstats_df['result'].value_counts()
    result_colors = {'W': 'green', 'D': 'yellow', 'L': 'red'}
    colors = [result_colors.get(result, 'gray') for result in results.index]
    axes[0,2].pie(results.values, labels=results.index, autopct='%1.1f%%', colors=colors)
    axes[0,2].set_title('Match Results Distribution')
    
    # 4.4 Shots on target efficiency
    teamstats_df['shot_accuracy'] = teamstats_df['shotsOnTarget'] / teamstats_df['shots']
    axes[1,0].hist(teamstats_df['shot_accuracy'].dropna(), bins=20, alpha=0.7, edgecolor='black', color='orange')
    axes[1,0].set_title('Shot Accuracy Distribution')
    axes[1,0].set_xlabel('Shots on Target / Total Shots')
    axes[1,0].set_ylabel('Number of Games')
    axes[1,0].grid(True, alpha=0.3)
    
    # 4.5 PPDA (Passes per Defensive Action) analysis
    axes[1,1].hist(teamstats_df['ppda'], bins=25, alpha=0.7, edgecolor='black', color='purple')
    axes[1,1].set_title('PPDA Distribution')
    axes[1,1].set_xlabel('Passes per Defensive Action')
    axes[1,1].set_ylabel('Number of Games')
    axes[1,1].grid(True, alpha=0.3)
    
    # 4.6 Performance over time
    monthly_performance = teamstats_df.groupby(teamstats_df['date'].dt.to_period('M')).agg({
        'goals': 'mean',
        'xGoals': 'mean'
    })
    
    monthly_performance['goals'].plot(ax=axes[1,2], label='Actual Goals', color='blue', linewidth=2)
    monthly_performance['xGoals'].plot(ax=axes[1,2], label='Expected Goals', color='red', linewidth=2)
    axes[1,2].set_title('Goals vs xGoals Over Time')
    axes[1,2].set_xlabel('Date')
    axes[1,2].set_ylabel('Average per Game')
    axes[1,2].legend()
    axes[1,2].grid(True, alpha=0.3)
    axes[1,2].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()

# 5. ADVANCED ANALYSIS AND INSIGHTS
print("\n🔍 ADVANCED FOOTBALL INSIGHTS")
print("="*50)

# Top performers analysis
if 'appearances' in dataframes and 'players' in dataframes:
    # Top goal scorers
    top_scorers = appearances_df.groupby('playerID')['goals'].sum().sort_values(ascending=False).head(10)
    top_scorers_with_names = top_scorers.reset_index().merge(dataframes['players'], on='playerID')
    
    print("\n🥅 TOP 10 GOAL SCORERS:")
    for _, player in top_scorers_with_names.iterrows():
        print(f"  {player['name']}: {player['goals']} goals")
    
    # Top assisters
    top_assisters = appearances_df.groupby('playerID')['assists'].sum().sort_values(ascending=False).head(10)
    top_assisters_with_names = top_assisters.reset_index().merge(dataframes['players'], on='playerID')
    
    print("\n🎯 TOP 10 ASSIST PROVIDERS:")
    for _, player in top_assisters_with_names.iterrows():
        print(f"  {player['name']}: {player['assists']} assists")

# Team performance analysis
if 'teamstats' in dataframes and 'teams' in dataframes:
    # Best attacking teams
    team_goals = teamstats_df.groupby('teamID')['goals'].mean().sort_values(ascending=False).head(10)
    team_goals_with_names = team_goals.reset_index().merge(dataframes['teams'], on='teamID')
    
    print("\n⚽ TOP 10 ATTACKING TEAMS (Average Goals per Game):")
    for _, team in team_goals_with_names.iterrows():
        print(f"  {team['name']}: {team['goals']:.2f} goals/game")
    
    # Best defensive teams (lowest goals conceded)
    # This would require opponent data, so we'll use shots conceded as proxy
    defensive_stats = teamstats_df.groupby('teamID').agg({
        'shots': 'mean',  # Shots faced
        'fouls': 'mean'
    }).sort_values('shots').head(10)
    defensive_with_names = defensive_stats.reset_index().merge(dataframes['teams'], on='teamID')
    
    print("\n🛡️ TOP 10 DEFENSIVE TEAMS (Fewest Shots Faced):")
    for _, team in defensive_with_names.iterrows():
        print(f"  {team['name']}: {team['shots']:.1f} shots faced/game")

# League comparison
if 'games' in dataframes and 'leagues' in dataframes:
    league_stats = games_df.groupby('leagueID').agg({
        'total_goals': 'mean',
        'homeGoals': 'mean',
        'awayGoals': 'mean'
    }).reset_index()
    league_stats = league_stats.merge(dataframes['leagues'], on='leagueID')
    league_stats['home_advantage'] = league_stats['homeGoals'] - league_stats['awayGoals']
    
    print("\n🏆 LEAGUE COMPARISON:")
    for _, league in league_stats.iterrows():
        print(f"  {league['name']}:")
        print(f"    Average goals/game: {league['total_goals']:.2f}")
        print(f"    Home advantage: +{league['home_advantage']:.2f} goals")

print("\n✅ Analysis complete! All visualizations and insights generated.")