# Football Data Analysis Suite

A comprehensive Python toolkit for football/soccer data analysis, featuring data exploration, statistical analysis, advanced visualizations, and machine learning predictions.

## 🚀 Features

### 📊 Data Analysis
- **Comprehensive Data Exploration**: Automated data profiling and statistical summaries
- **Data Cleaning & Preprocessing**: Handle missing values, outliers, and data standardization
- **Team Performance Analysis**: League tables, head-to-head records, and performance metrics
- **Statistical Insights**: Home advantage analysis, goal distributions, and correlation analysis

### 📈 Advanced Visualizations
- **Interactive Dashboards**: Plotly-based interactive charts and tables
- **Team Performance Radar Charts**: Multi-dimensional team comparison
- **Goals Timeline Analysis**: Track scoring patterns over time
- **Heat Maps**: Head-to-head results and correlation matrices
- **Performance Trends**: Rolling averages and form analysis

### 🤖 Machine Learning Models
- **Match Result Prediction**: Predict Win/Draw/Loss outcomes
- **Goals Prediction**: Forecast total goals in matches
- **Team Performance Modeling**: Predict points, goals, and other metrics
- **Cross-Validation**: Robust model evaluation
- **Hyperparameter Tuning**: Optimize model performance

### 🛠️ Data Preprocessing Tools
- **Team Name Standardization**: Handle different naming conventions
- **Date Parsing**: Extract temporal features from match dates
- **Score Data Cleaning**: Parse score strings and calculate derived metrics
- **Feature Engineering**: Create advanced statistical features
- **Outlier Detection**: Identify and handle anomalous data points

## 📁 Project Structure

```
football data/
├── football_analysis.py          # Main analysis class with complete pipeline
├── data_preprocessing.py          # Data cleaning and preprocessing utilities
├── advanced_visualizations.py    # Interactive and advanced plotting functions
├── ml_models.py                  # Machine learning models and predictions
├── requirements.txt              # Python package dependencies
├── README.md                     # This file
└── sample_analysis.py            # Complete example workflow
```

## 🔧 Installation

1. **Install Required Packages**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Required Python Packages**:
   - pandas >= 1.5.0
   - numpy >= 1.21.0
   - matplotlib >= 3.5.0
   - seaborn >= 0.11.0
   - scikit-learn >= 1.1.0
   - plotly >= 5.0.0
   - jupyter >= 1.0.0
   - openpyxl >= 3.0.0

## 🚀 Quick Start

### Option 1: Complete Analysis with Sample Data
```python
from football_analysis import FootballDataAnalyzer

# Initialize analyzer
analyzer = FootballDataAnalyzer()

# Run complete analysis pipeline
analyzer.run_complete_analysis()
```

### Option 2: Load Your Own Data
```python
from football_analysis import FootballDataAnalyzer

# Initialize analyzer
analyzer = FootballDataAnalyzer()

# Load your data
analyzer.load_data('your_football_data.csv')

# Run analysis
analyzer.run_complete_analysis()
```

### Option 3: Step-by-Step Analysis
```python
from football_analysis import FootballDataAnalyzer

analyzer = FootballDataAnalyzer()

# Create or load data
analyzer.create_sample_data()  # or analyzer.load_data('file.csv')

# Individual analysis steps
analyzer.explore_data()
analyzer.clean_data()
analyzer.create_visualizations()
analyzer.statistical_analysis()
analyzer.build_predictive_models()
analyzer.generate_insights()
```

## 📊 Data Format

The toolkit expects football match data with the following structure:

### Required Columns:
- `home_team`: Name of home team
- `away_team`: Name of away team
- `home_goals`: Goals scored by home team
- `away_goals`: Goals scored by away team

### Optional Columns (for enhanced analysis):
- `date`: Match date
- `home_shots`, `away_shots`: Shot statistics
- `home_possession`, `away_possession`: Possession percentages
- `home_corners`, `away_corners`: Corner kick counts
- `home_fouls`, `away_fouls`: Foul counts
- `home_yellow_cards`, `away_yellow_cards`: Yellow card counts
- `home_red_cards`, `away_red_cards`: Red card counts
- `attendance`: Match attendance
- `season`: Season identifier
- `matchday`: Matchday number

### Example CSV Format:
```csv
home_team,away_team,home_goals,away_goals,home_shots,away_shots,home_possession,away_possession,date
Arsenal,Chelsea,2,1,15,8,58,42,2023-10-15
Manchester United,Liverpool,1,3,12,18,45,55,2023-10-16
```

## 🎯 Key Features in Detail

### 1. Data Exploration
- Dataset shape and structure analysis
- Missing value detection and handling
- Basic statistical summaries
- Data type validation

### 2. Team Performance Analysis
- Automatic league table generation
- Points per game calculations
- Goal scoring and defensive metrics
- Home vs away performance comparison

### 3. Statistical Analysis
- Home advantage quantification
- Goal distribution analysis
- Correlation analysis between variables
- Performance trend identification

### 4. Advanced Visualizations
- **Radar Charts**: Multi-dimensional team performance comparison
- **Timeline Plots**: Goals and performance trends over time
- **Heat Maps**: Head-to-head results and statistical correlations
- **Interactive Tables**: Sortable and filterable league tables
- **Distribution Plots**: Goal and result distributions

### 5. Machine Learning Models
- **Classification Models**: Random Forest, Logistic Regression, SVM, Naive Bayes
- **Regression Models**: Predict total goals and team metrics
- **Model Evaluation**: Cross-validation and performance metrics
- **Feature Importance**: Identify key predictive factors

## 📈 Example Outputs

### League Table
```
   position              team  matches_played  wins  draws  losses  points
0         1           Team_15              19    12      4       3      40
1         2            Team_3              19    11      5       3      38
2         3           Team_12              19    11      4       4      37
```

### Model Performance
```
Random Forest Accuracy: 0.487
Linear Regression RMSE: 1.234

Top 5 features for Random Forest:
         feature  importance
0    home_shots    0.234567
1  home_possession 0.198765
2    away_shots    0.156789
```

### Key Insights
```
KEY INSIGHTS FROM FOOTBALL DATA ANALYSIS
================================================
1. Strong home advantage detected: 45.3% of matches won by home team
2. High-scoring league: Average of 2.8 goals per match
3. Home teams dominate possession: 52.1% average
```

## 🔍 Advanced Usage

### Custom Preprocessing
```python
from data_preprocessing import FootballDataPreprocessor

preprocessor = FootballDataPreprocessor()

# Standardize team names
df = preprocessor.standardize_team_names(df, ['home_team', 'away_team'])

# Parse dates and create features
df = preprocessor.parse_match_date(df, 'date')

# Create league table
table = preprocessor.create_league_table(df)
```

### Advanced Visualizations
```python
from advanced_visualizations import FootballVisualizer

visualizer = FootballVisualizer()

# Create team radar chart
visualizer.create_team_performance_radar(team_stats, 'Arsenal')

# Create interactive dashboard
visualizer.create_dashboard(matches_df, team_stats_df)
```

### Machine Learning
```python
from ml_models import FootballMLModels

ml_models = FootballMLModels()

# Predict match results
results = ml_models.predict_match_results(matches_df)

# Predict total goals
goals_results = ml_models.predict_total_goals(matches_df)

# Cross-validate models
cv_results = ml_models.cross_validate_models(matches_df)
```

## 🤝 Contributing

Feel free to contribute to this project by:
1. Adding new analysis features
2. Improving existing algorithms
3. Adding support for new data formats
4. Enhancing visualizations
5. Optimizing performance

## 📝 License

This project is open source and available under the MIT License.

## 🆘 Support

For questions, issues, or feature requests, please refer to the documentation in each Python file or create an issue in the project repository.

## 🎯 Future Enhancements

- [ ] Real-time data integration
- [ ] Player-level analysis
- [ ] Advanced tactical analysis
- [ ] Web dashboard interface
- [ ] API for external data sources
- [ ] Mobile app integration
- [ ] Advanced deep learning models