"""
Football Data Analysis Suite
A comprehensive Python toolkit for football/soccer data analysis

This script provides functionality for:
- Data exploration and cleaning
- Statistical analysis
- Visualization
- Predictive modeling
- Performance metrics
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.metrics import mean_squared_error, accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class FootballDataAnalyzer:
    """
    A comprehensive class for football data analysis
    """
    
    def __init__(self):
        self.data = None
        self.cleaned_data = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        
    def load_data(self, file_path, file_type='csv'):
        """
        Load football data from various file formats
        
        Parameters:
        file_path (str): Path to the data file
        file_type (str): Type of file ('csv', 'excel', 'json')
        """
        try:
            if file_type.lower() == 'csv':
                self.data = pd.read_csv(file_path)
            elif file_type.lower() in ['excel', 'xlsx']:
                self.data = pd.read_excel(file_path)
            elif file_type.lower() == 'json':
                self.data = pd.read_json(file_path)
            else:
                raise ValueError("Unsupported file type. Use 'csv', 'excel', or 'json'")
                
            print(f"Data loaded successfully! Shape: {self.data.shape}")
            return self.data
            
        except Exception as e:
            print(f"Error loading data: {str(e)}")
            return None
    
    def create_sample_data(self, n_teams=20, n_matches=380):
        """
        Create sample football data for demonstration
        
        Parameters:
        n_teams (int): Number of teams
        n_matches (int): Number of matches
        """
        np.random.seed(42)
        
        # Team names
        teams = [f"Team_{i+1}" for i in range(n_teams)]
        
        # Generate match data
        matches_data = []
        for i in range(n_matches):
            home_team = np.random.choice(teams)
            away_team = np.random.choice([t for t in teams if t != home_team])
            
            # Simulate match statistics
            home_goals = np.random.poisson(1.5)
            away_goals = np.random.poisson(1.2)
            
            match = {
                'match_id': i + 1,
                'home_team': home_team,
                'away_team': away_team,
                'home_goals': home_goals,
                'away_goals': away_goals,
                'home_shots': np.random.randint(8, 25),
                'away_shots': np.random.randint(8, 25),
                'home_possession': np.random.uniform(30, 70),
                'away_possession': 100 - np.random.uniform(30, 70),
                'home_corners': np.random.randint(0, 12),
                'away_corners': np.random.randint(0, 12),
                'home_fouls': np.random.randint(5, 20),
                'away_fouls': np.random.randint(5, 20),
                'home_yellow_cards': np.random.randint(0, 5),
                'away_yellow_cards': np.random.randint(0, 5),
                'home_red_cards': np.random.randint(0, 2),
                'away_red_cards': np.random.randint(0, 2),
                'attendance': np.random.randint(20000, 80000),
                'season': '2023-24',
                'matchday': (i % 38) + 1
            }
            
            # Determine result
            if home_goals > away_goals:
                match['result'] = 'H'  # Home win
                match['home_points'] = 3
                match['away_points'] = 0
            elif away_goals > home_goals:
                match['result'] = 'A'  # Away win
                match['home_points'] = 0
                match['away_points'] = 3
            else:
                match['result'] = 'D'  # Draw
                match['home_points'] = 1
                match['away_points'] = 1
                
            matches_data.append(match)
        
        self.data = pd.DataFrame(matches_data)
        print(f"Sample data created! Shape: {self.data.shape}")
        return self.data
    
    def explore_data(self):
        """
        Perform comprehensive data exploration
        """
        if self.data is None:
            print("No data loaded. Please load data first.")
            return
        
        print("=== DATA EXPLORATION ===")
        print(f"Dataset shape: {self.data.shape}")
        print(f"Columns: {list(self.data.columns)}")
        print("\n=== DATA TYPES ===")
        print(self.data.dtypes)
        print("\n=== MISSING VALUES ===")
        print(self.data.isnull().sum())
        print("\n=== BASIC STATISTICS ===")
        print(self.data.describe())
        print("\n=== FIRST 5 ROWS ===")
        print(self.data.head())
        
        return self.data.info()
    
    def clean_data(self):
        """
        Clean and preprocess the data
        """
        if self.data is None:
            print("No data loaded. Please load data first.")
            return
        
        print("=== DATA CLEANING ===")
        self.cleaned_data = self.data.copy()
        
        # Handle missing values
        numeric_columns = self.cleaned_data.select_dtypes(include=[np.number]).columns
        categorical_columns = self.cleaned_data.select_dtypes(include=['object']).columns
        
        # Fill numeric missing values with median
        for col in numeric_columns:
            if self.cleaned_data[col].isnull().sum() > 0:
                self.cleaned_data[col].fillna(self.cleaned_data[col].median(), inplace=True)
                print(f"Filled missing values in {col} with median")
        
        # Fill categorical missing values with mode
        for col in categorical_columns:
            if self.cleaned_data[col].isnull().sum() > 0:
                self.cleaned_data[col].fillna(self.cleaned_data[col].mode()[0], inplace=True)
                print(f"Filled missing values in {col} with mode")
        
        # Remove duplicates
        initial_rows = len(self.cleaned_data)
        self.cleaned_data.drop_duplicates(inplace=True)
        removed_duplicates = initial_rows - len(self.cleaned_data)
        if removed_duplicates > 0:
            print(f"Removed {removed_duplicates} duplicate rows")
        
        # Add derived features
        if 'home_goals' in self.cleaned_data.columns and 'away_goals' in self.cleaned_data.columns:
            self.cleaned_data['total_goals'] = self.cleaned_data['home_goals'] + self.cleaned_data['away_goals']
            self.cleaned_data['goal_difference'] = self.cleaned_data['home_goals'] - self.cleaned_data['away_goals']
            print("Added derived features: total_goals, goal_difference")
        
        print(f"Data cleaning completed. Final shape: {self.cleaned_data.shape}")
        return self.cleaned_data
    
    def create_visualizations(self):
        """
        Create comprehensive visualizations
        """
        if self.cleaned_data is None:
            print("No cleaned data available. Please clean data first.")
            return
        
        print("=== CREATING VISUALIZATIONS ===")
        
        # Set up the plotting area
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Football Data Analysis Dashboard', fontsize=16, fontweight='bold')
        
        # 1. Goals distribution
        if 'total_goals' in self.cleaned_data.columns:
            axes[0, 0].hist(self.cleaned_data['total_goals'], bins=15, alpha=0.7, color='skyblue', edgecolor='black')
            axes[0, 0].set_title('Distribution of Total Goals per Match')
            axes[0, 0].set_xlabel('Total Goals')
            axes[0, 0].set_ylabel('Frequency')
        
        # 2. Result distribution
        if 'result' in self.cleaned_data.columns:
            result_counts = self.cleaned_data['result'].value_counts()
            axes[0, 1].pie(result_counts.values, labels=['Home Win', 'Draw', 'Away Win'], autopct='%1.1f%%')
            axes[0, 1].set_title('Match Results Distribution')
        
        # 3. Goals vs Shots correlation
        if all(col in self.cleaned_data.columns for col in ['home_goals', 'home_shots']):
            axes[0, 2].scatter(self.cleaned_data['home_shots'], self.cleaned_data['home_goals'], alpha=0.6)
            axes[0, 2].set_title('Home Goals vs Home Shots')
            axes[0, 2].set_xlabel('Home Shots')
            axes[0, 2].set_ylabel('Home Goals')
        
        # 4. Possession distribution
        if 'home_possession' in self.cleaned_data.columns:
            axes[1, 0].hist(self.cleaned_data['home_possession'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
            axes[1, 0].set_title('Home Team Possession Distribution')
            axes[1, 0].set_xlabel('Possession %')
            axes[1, 0].set_ylabel('Frequency')
        
        # 5. Goals by matchday
        if all(col in self.cleaned_data.columns for col in ['matchday', 'total_goals']):
            matchday_goals = self.cleaned_data.groupby('matchday')['total_goals'].mean()
            axes[1, 1].plot(matchday_goals.index, matchday_goals.values, marker='o')
            axes[1, 1].set_title('Average Goals per Matchday')
            axes[1, 1].set_xlabel('Matchday')
            axes[1, 1].set_ylabel('Average Goals')
        
        # 6. Correlation heatmap
        numeric_cols = self.cleaned_data.select_dtypes(include=[np.number]).columns[:8]  # Limit to first 8 numeric columns
        if len(numeric_cols) > 1:
            correlation_matrix = self.cleaned_data[numeric_cols].corr()
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, ax=axes[1, 2])
            axes[1, 2].set_title('Correlation Matrix')
        
        plt.tight_layout()
        plt.show()
        
        # Additional team-specific analysis
        if 'home_team' in self.cleaned_data.columns:
            self.analyze_team_performance()
    
    def analyze_team_performance(self):
        """
        Analyze team performance metrics
        """
        print("\n=== TEAM PERFORMANCE ANALYSIS ===")
        
        # Create team statistics
        team_stats = []
        
        teams = list(set(self.cleaned_data['home_team'].unique().tolist() + 
                        self.cleaned_data['away_team'].unique().tolist()))
        
        for team in teams:
            home_matches = self.cleaned_data[self.cleaned_data['home_team'] == team]
            away_matches = self.cleaned_data[self.cleaned_data['away_team'] == team]
            
            # Calculate statistics
            total_matches = len(home_matches) + len(away_matches)
            total_points = home_matches['home_points'].sum() + away_matches['away_points'].sum()
            goals_scored = home_matches['home_goals'].sum() + away_matches['away_goals'].sum()
            goals_conceded = home_matches['away_goals'].sum() + away_matches['home_goals'].sum()
            
            wins = len(home_matches[home_matches['result'] == 'H']) + len(away_matches[away_matches['result'] == 'A'])
            draws = len(home_matches[home_matches['result'] == 'D']) + len(away_matches[away_matches['result'] == 'D'])
            losses = total_matches - wins - draws
            
            team_stats.append({
                'team': team,
                'matches_played': total_matches,
                'wins': wins,
                'draws': draws,
                'losses': losses,
                'points': total_points,
                'goals_scored': goals_scored,
                'goals_conceded': goals_conceded,
                'goal_difference': goals_scored - goals_conceded,
                'points_per_game': total_points / total_matches if total_matches > 0 else 0,
                'goals_per_game': goals_scored / total_matches if total_matches > 0 else 0
            })
        
        team_df = pd.DataFrame(team_stats)
        team_df = team_df.sort_values('points', ascending=False).reset_index(drop=True)
        team_df['position'] = range(1, len(team_df) + 1)
        
        print("League Table (Top 10):")
        print(team_df[['position', 'team', 'matches_played', 'wins', 'draws', 'losses', 
                      'points', 'goals_scored', 'goals_conceded', 'goal_difference']].head(10))
        
        # Visualize team performance
        plt.figure(figsize=(15, 10))
        
        # Top 10 teams by points
        plt.subplot(2, 2, 1)
        top_teams = team_df.head(10)
        plt.barh(top_teams['team'], top_teams['points'])
        plt.title('Top 10 Teams by Points')
        plt.xlabel('Points')
        
        # Goals scored vs conceded
        plt.subplot(2, 2, 2)
        plt.scatter(team_df['goals_scored'], team_df['goals_conceded'], alpha=0.7)
        plt.xlabel('Goals Scored')
        plt.ylabel('Goals Conceded')
        plt.title('Goals Scored vs Goals Conceded')
        
        # Points per game distribution
        plt.subplot(2, 2, 3)
        plt.hist(team_df['points_per_game'], bins=15, alpha=0.7, color='orange', edgecolor='black')
        plt.xlabel('Points per Game')
        plt.ylabel('Number of Teams')
        plt.title('Points per Game Distribution')
        
        # Win rate vs goal difference
        plt.subplot(2, 2, 4)
        team_df['win_rate'] = team_df['wins'] / team_df['matches_played'] * 100
        plt.scatter(team_df['goal_difference'], team_df['win_rate'], alpha=0.7)
        plt.xlabel('Goal Difference')
        plt.ylabel('Win Rate (%)')
        plt.title('Goal Difference vs Win Rate')
        
        plt.tight_layout()
        plt.show()
        
        return team_df
    
    def statistical_analysis(self):
        """
        Perform statistical analysis
        """
        if self.cleaned_data is None:
            print("No cleaned data available. Please clean data first.")
            return
        
        print("\n=== STATISTICAL ANALYSIS ===")
        
        # Home advantage analysis
        if 'result' in self.cleaned_data.columns:
            home_wins = len(self.cleaned_data[self.cleaned_data['result'] == 'H'])
            away_wins = len(self.cleaned_data[self.cleaned_data['result'] == 'A'])
            draws = len(self.cleaned_data[self.cleaned_data['result'] == 'D'])
            total_matches = len(self.cleaned_data)
            
            print(f"Home Win Rate: {home_wins/total_matches*100:.1f}%")
            print(f"Away Win Rate: {away_wins/total_matches*100:.1f}%")
            print(f"Draw Rate: {draws/total_matches*100:.1f}%")
            print(f"Home Advantage: {(home_wins-away_wins)/total_matches*100:.1f} percentage points")
        
        # Goals analysis
        if 'total_goals' in self.cleaned_data.columns:
            avg_goals = self.cleaned_data['total_goals'].mean()
            print(f"\nAverage Goals per Match: {avg_goals:.2f}")
            print(f"Most Goals in a Match: {self.cleaned_data['total_goals'].max()}")
            print(f"Matches with 0 Goals: {len(self.cleaned_data[self.cleaned_data['total_goals'] == 0])}")
            print(f"Matches with 5+ Goals: {len(self.cleaned_data[self.cleaned_data['total_goals'] >= 5])}")
        
        # Correlation analysis
        numeric_columns = self.cleaned_data.select_dtypes(include=[np.number]).columns
        if len(numeric_columns) > 1:
            print(f"\nTop Correlations:")
            corr_matrix = self.cleaned_data[numeric_columns].corr()
            
            # Get top correlations (excluding self-correlations)
            correlations = []
            for i in range(len(corr_matrix.columns)):
                for j in range(i+1, len(corr_matrix.columns)):
                    correlations.append({
                        'var1': corr_matrix.columns[i],
                        'var2': corr_matrix.columns[j],
                        'correlation': corr_matrix.iloc[i, j]
                    })
            
            correlations_df = pd.DataFrame(correlations)
            correlations_df = correlations_df.reindex(correlations_df['correlation'].abs().sort_values(ascending=False).index)
            
            print(correlations_df.head(10))
    
    def build_predictive_models(self):
        """
        Build predictive models for match outcomes
        """
        if self.cleaned_data is None:
            print("No cleaned data available. Please clean data first.")
            return
        
        print("\n=== BUILDING PREDICTIVE MODELS ===")
        
        # Prepare features for modeling
        feature_columns = []
        for col in self.cleaned_data.columns:
            if col in ['home_shots', 'away_shots', 'home_possession', 'away_possession', 
                      'home_corners', 'away_corners', 'home_fouls', 'away_fouls',
                      'home_yellow_cards', 'away_yellow_cards', 'attendance']:
                feature_columns.append(col)
        
        if len(feature_columns) < 3:
            print("Not enough features for modeling.")
            return
        
        X = self.cleaned_data[feature_columns]
        
        # Model 1: Predict total goals (regression)
        if 'total_goals' in self.cleaned_data.columns:
            print("\n--- Predicting Total Goals ---")
            y_goals = self.cleaned_data['total_goals']
            
            X_train, X_test, y_train, y_test = train_test_split(X, y_goals, test_size=0.2, random_state=42)
            
            # Random Forest Regressor
            rf_reg = RandomForestRegressor(n_estimators=100, random_state=42)
            rf_reg.fit(X_train, y_train)
            y_pred_rf = rf_reg.predict(X_test)
            
            # Linear Regression
            lr_reg = LinearRegression()
            lr_reg.fit(X_train, y_train)
            y_pred_lr = lr_reg.predict(X_test)
            
            print(f"Random Forest RMSE: {np.sqrt(mean_squared_error(y_test, y_pred_rf)):.3f}")
            print(f"Linear Regression RMSE: {np.sqrt(mean_squared_error(y_test, y_pred_lr)):.3f}")
            
            # Feature importance
            feature_importance = pd.DataFrame({
                'feature': feature_columns,
                'importance': rf_reg.feature_importances_
            }).sort_values('importance', ascending=False)
            
            print("\nFeature Importance for Goals Prediction:")
            print(feature_importance)
        
        # Model 2: Predict match result (classification)
        if 'result' in self.cleaned_data.columns:
            print("\n--- Predicting Match Result ---")
            y_result = self.cleaned_data['result']
            
            X_train, X_test, y_train, y_test = train_test_split(X, y_result, test_size=0.2, random_state=42)
            
            # Random Forest Classifier
            rf_clf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf_clf.fit(X_train, y_train)
            y_pred_rf = rf_clf.predict(X_test)
            
            print(f"Random Forest Accuracy: {accuracy_score(y_test, y_pred_rf):.3f}")
            print("\nClassification Report:")
            print(classification_report(y_test, y_pred_rf))
            
            # Feature importance for classification
            feature_importance_clf = pd.DataFrame({
                'feature': feature_columns,
                'importance': rf_clf.feature_importances_
            }).sort_values('importance', ascending=False)
            
            print("\nFeature Importance for Result Prediction:")
            print(feature_importance_clf)
    
    def generate_insights(self):
        """
        Generate key insights from the analysis
        """
        if self.cleaned_data is None:
            print("No cleaned data available. Please clean data first.")
            return
        
        print("\n" + "="*50)
        print("KEY INSIGHTS FROM FOOTBALL DATA ANALYSIS")
        print("="*50)
        
        insights = []
        
        # Home advantage insight
        if 'result' in self.cleaned_data.columns:
            home_win_rate = len(self.cleaned_data[self.cleaned_data['result'] == 'H']) / len(self.cleaned_data) * 100
            if home_win_rate > 40:
                insights.append(f"Strong home advantage detected: {home_win_rate:.1f}% of matches won by home team")
            elif home_win_rate < 30:
                insights.append(f"Weak home advantage: Only {home_win_rate:.1f}% of matches won by home team")
        
        # Goals insight
        if 'total_goals' in self.cleaned_data.columns:
            avg_goals = self.cleaned_data['total_goals'].mean()
            if avg_goals > 3:
                insights.append(f"High-scoring league: Average of {avg_goals:.1f} goals per match")
            elif avg_goals < 2:
                insights.append(f"Low-scoring league: Average of {avg_goals:.1f} goals per match")
        
        # Possession insight
        if 'home_possession' in self.cleaned_data.columns:
            avg_home_possession = self.cleaned_data['home_possession'].mean()
            if avg_home_possession > 55:
                insights.append(f"Home teams dominate possession: {avg_home_possession:.1f}% average")
            elif avg_home_possession < 45:
                insights.append(f"Away teams control possession: {100-avg_home_possession:.1f}% average")
        
        # Display insights
        for i, insight in enumerate(insights, 1):
            print(f"{i}. {insight}")
        
        if not insights:
            print("No significant patterns detected in the current dataset.")
        
        print("="*50)
    
    def run_complete_analysis(self):
        """
        Run the complete analysis pipeline
        """
        print("Starting Complete Football Data Analysis...")
        print("="*60)
        
        # Create sample data if no data is loaded
        if self.data is None:
            print("No data found. Creating sample data...")
            self.create_sample_data()
        
        # Run analysis pipeline
        self.explore_data()
        self.clean_data()
        self.create_visualizations()
        self.statistical_analysis()
        self.build_predictive_models()
        self.generate_insights()
        
        print("\n" + "="*60)
        print("ANALYSIS COMPLETE!")
        print("="*60)

# Example usage and main execution
if __name__ == "__main__":
    # Initialize the analyzer
    analyzer = FootballDataAnalyzer()
    
    print("Football Data Analysis Suite")
    print("="*40)
    print("Available methods:")
    print("1. analyzer.create_sample_data() - Create sample data")
    print("2. analyzer.load_data('file_path') - Load your own data")
    print("3. analyzer.run_complete_analysis() - Run full analysis")
    print("4. Individual methods: explore_data(), clean_data(), create_visualizations(), etc.")
    print("\nTo get started with sample data, run:")
    print("analyzer.run_complete_analysis()")
    
    # Uncomment the line below to run complete analysis automatically
    # analyzer.run_complete_analysis()