"""
Data Preprocessing Utilities for Football Analysis
Specialized functions for cleaning and preparing football data
"""

import pandas as pd
import numpy as np
from datetime import datetime
import re

class FootballDataPreprocessor:
    """
    Specialized preprocessing class for football data
    """
    
    def __init__(self):
        self.team_name_mappings = {}
        self.position_mappings = {
            'GK': 'Goalkeeper', 'DEF': 'Defender', 'MID': 'Midfielder', 
            'FWD': 'Forward', 'ATT': 'Attacker'
        }
    
    def standardize_team_names(self, df, team_columns):
        """
        Standardize team names across different formats
        
        Parameters:
        df (DataFrame): Input dataframe
        team_columns (list): List of columns containing team names
        """
        df_clean = df.copy()
        
        for col in team_columns:
            if col in df_clean.columns:
                # Remove extra spaces and standardize case
                df_clean[col] = df_clean[col].str.strip().str.title()
                
                # Common team name standardizations
                replacements = {
                    'Man United': 'Manchester United',
                    'Man City': 'Manchester City',
                    'Tottenham': 'Tottenham Hotspur',
                    'Brighton': 'Brighton & Hove Albion',
                    'Newcastle': 'Newcastle United',
                    'West Ham': 'West Ham United',
                    'Wolves': 'Wolverhampton Wanderers'
                }
                
                for old_name, new_name in replacements.items():
                    df_clean[col] = df_clean[col].replace(old_name, new_name)
        
        return df_clean
    
    def parse_match_date(self, df, date_column, date_format=None):
        """
        Parse and standardize match dates
        
        Parameters:
        df (DataFrame): Input dataframe
        date_column (str): Name of the date column
        date_format (str): Expected date format (optional)
        """
        df_clean = df.copy()
        
        if date_column in df_clean.columns:
            try:
                if date_format:
                    df_clean[date_column] = pd.to_datetime(df_clean[date_column], format=date_format)
                else:
                    df_clean[date_column] = pd.to_datetime(df_clean[date_column], infer_datetime_format=True)
                
                # Extract additional date features
                df_clean['year'] = df_clean[date_column].dt.year
                df_clean['month'] = df_clean[date_column].dt.month
                df_clean['day_of_week'] = df_clean[date_column].dt.day_name()
                df_clean['is_weekend'] = df_clean[date_column].dt.weekday >= 5
                
                print(f"Successfully parsed {date_column} and created date features")
                
            except Exception as e:
                print(f"Error parsing dates: {str(e)}")
        
        return df_clean
    
    def clean_score_data(self, df, score_column):
        """
        Clean and parse score data (e.g., "2-1", "0-0")
        
        Parameters:
        df (DataFrame): Input dataframe
        score_column (str): Name of the score column
        """
        df_clean = df.copy()
        
        if score_column in df_clean.columns:
            # Extract home and away goals from score strings
            score_pattern = r'(\d+)-(\d+)'
            
            df_clean['home_goals'] = df_clean[score_column].str.extract(score_pattern)[0].astype(float)
            df_clean['away_goals'] = df_clean[score_column].str.extract(score_pattern)[1].astype(float)
            
            # Calculate derived metrics
            df_clean['total_goals'] = df_clean['home_goals'] + df_clean['away_goals']
            df_clean['goal_difference'] = df_clean['home_goals'] - df_clean['away_goals']
            
            # Determine result
            conditions = [
                df_clean['home_goals'] > df_clean['away_goals'],
                df_clean['home_goals'] < df_clean['away_goals'],
                df_clean['home_goals'] == df_clean['away_goals']
            ]
            choices = ['H', 'A', 'D']
            df_clean['result'] = np.select(conditions, choices)
            
            print(f"Successfully parsed {score_column} and created goal-related features")
        
        return df_clean
    
    def handle_player_data(self, df):
        """
        Clean and standardize player data
        
        Parameters:
        df (DataFrame): Player dataframe
        """
        df_clean = df.copy()
        
        # Standardize player names
        if 'player_name' in df_clean.columns:
            df_clean['player_name'] = df_clean['player_name'].str.strip().str.title()
        
        # Clean position data
        if 'position' in df_clean.columns:
            df_clean['position'] = df_clean['position'].str.upper().str.strip()
            df_clean['position_full'] = df_clean['position'].map(self.position_mappings).fillna(df_clean['position'])
        
        # Handle age data
        if 'age' in df_clean.columns:
            df_clean['age'] = pd.to_numeric(df_clean['age'], errors='coerce')
            df_clean['age_group'] = pd.cut(df_clean['age'], 
                                         bins=[0, 21, 25, 30, 35, 100], 
                                         labels=['U21', '21-25', '26-30', '31-35', '35+'])
        
        # Handle height and weight
        for col in ['height', 'weight']:
            if col in df_clean.columns:
                # Remove units and convert to numeric
                df_clean[col] = df_clean[col].astype(str).str.extract(r'(\d+\.?\d*)')[0].astype(float)
        
        return df_clean
    
    def calculate_team_form(self, df, team_col, result_col, n_matches=5):
        """
        Calculate team form based on recent matches
        
        Parameters:
        df (DataFrame): Match dataframe
        team_col (str): Team column name
        result_col (str): Result column name
        n_matches (int): Number of recent matches to consider
        """
        df_clean = df.copy()
        
        # Sort by date if available
        if 'date' in df_clean.columns:
            df_clean = df_clean.sort_values('date')
        
        form_data = []
        
        for team in df_clean[team_col].unique():
            team_matches = df_clean[df_clean[team_col] == team].tail(n_matches)
            
            # Calculate form points (3 for win, 1 for draw, 0 for loss)
            points = 0
            for _, match in team_matches.iterrows():
                if match[result_col] == 'W':
                    points += 3
                elif match[result_col] == 'D':
                    points += 1
            
            form_data.append({
                'team': team,
                'form_points': points,
                'form_matches': len(team_matches),
                'form_ppg': points / len(team_matches) if len(team_matches) > 0 else 0
            })
        
        form_df = pd.DataFrame(form_data)
        return form_df
    
    def create_match_features(self, df):
        """
        Create additional features for match analysis
        
        Parameters:
        df (DataFrame): Match dataframe
        """
        df_clean = df.copy()
        
        # Goal-related features
        if all(col in df_clean.columns for col in ['home_goals', 'away_goals']):
            df_clean['is_high_scoring'] = (df_clean['home_goals'] + df_clean['away_goals']) >= 3
            df_clean['is_clean_sheet_home'] = df_clean['away_goals'] == 0
            df_clean['is_clean_sheet_away'] = df_clean['home_goals'] == 0
            df_clean['goal_margin'] = abs(df_clean['home_goals'] - df_clean['away_goals'])
        
        # Possession-related features
        if 'home_possession' in df_clean.columns:
            df_clean['possession_dominance'] = abs(df_clean['home_possession'] - 50)
            df_clean['home_possession_advantage'] = df_clean['home_possession'] > 50
        
        # Shot efficiency
        if all(col in df_clean.columns for col in ['home_goals', 'home_shots']):
            df_clean['home_shot_accuracy'] = df_clean['home_goals'] / df_clean['home_shots'].replace(0, np.nan)
        
        if all(col in df_clean.columns for col in ['away_goals', 'away_shots']):
            df_clean['away_shot_accuracy'] = df_clean['away_goals'] / df_clean['away_shots'].replace(0, np.nan)
        
        # Card discipline
        if all(col in df_clean.columns for col in ['home_yellow_cards', 'home_red_cards']):
            df_clean['home_total_cards'] = df_clean['home_yellow_cards'] + (df_clean['home_red_cards'] * 2)
        
        if all(col in df_clean.columns for col in ['away_yellow_cards', 'away_red_cards']):
            df_clean['away_total_cards'] = df_clean['away_yellow_cards'] + (df_clean['away_red_cards'] * 2)
        
        return df_clean
    
    def detect_outliers(self, df, columns, method='iqr'):
        """
        Detect outliers in specified columns
        
        Parameters:
        df (DataFrame): Input dataframe
        columns (list): Columns to check for outliers
        method (str): Method to use ('iqr' or 'zscore')
        """
        outliers_info = {}
        
        for col in columns:
            if col in df.columns and df[col].dtype in ['int64', 'float64']:
                if method == 'iqr':
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
                    
                elif method == 'zscore':
                    z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                    outliers = df[z_scores > 3]
                
                outliers_info[col] = {
                    'count': len(outliers),
                    'percentage': len(outliers) / len(df) * 100,
                    'values': outliers[col].tolist()
                }
        
        return outliers_info
    
    def create_league_table(self, matches_df):
        """
        Create a league table from match results
        
        Parameters:
        matches_df (DataFrame): DataFrame with match results
        """
        if not all(col in matches_df.columns for col in ['home_team', 'away_team', 'home_goals', 'away_goals']):
            print("Required columns missing for league table creation")
            return None
        
        teams = list(set(matches_df['home_team'].unique().tolist() + 
                        matches_df['away_team'].unique().tolist()))
        
        table_data = []
        
        for team in teams:
            home_matches = matches_df[matches_df['home_team'] == team]
            away_matches = matches_df[matches_df['away_team'] == team]
            
            # Home statistics
            home_wins = len(home_matches[home_matches['home_goals'] > home_matches['away_goals']])
            home_draws = len(home_matches[home_matches['home_goals'] == home_matches['away_goals']])
            home_losses = len(home_matches[home_matches['home_goals'] < home_matches['away_goals']])
            home_gf = home_matches['home_goals'].sum()
            home_ga = home_matches['away_goals'].sum()
            
            # Away statistics
            away_wins = len(away_matches[away_matches['away_goals'] > away_matches['home_goals']])
            away_draws = len(away_matches[away_matches['away_goals'] == away_matches['home_goals']])
            away_losses = len(away_matches[away_matches['away_goals'] < away_matches['home_goals']])
            away_gf = away_matches['away_goals'].sum()
            away_ga = away_matches['home_goals'].sum()
            
            # Total statistics
            total_wins = home_wins + away_wins
            total_draws = home_draws + away_draws
            total_losses = home_losses + away_losses
            total_gf = home_gf + away_gf
            total_ga = home_ga + away_ga
            total_matches = len(home_matches) + len(away_matches)
            total_points = (total_wins * 3) + total_draws
            
            table_data.append({
                'team': team,
                'matches_played': total_matches,
                'wins': total_wins,
                'draws': total_draws,
                'losses': total_losses,
                'goals_for': total_gf,
                'goals_against': total_ga,
                'goal_difference': total_gf - total_ga,
                'points': total_points,
                'points_per_game': total_points / total_matches if total_matches > 0 else 0
            })
        
        table_df = pd.DataFrame(table_data)
        table_df = table_df.sort_values(['points', 'goal_difference', 'goals_for'], 
                                       ascending=[False, False, False]).reset_index(drop=True)
        table_df['position'] = range(1, len(table_df) + 1)
        
        return table_df[['position', 'team', 'matches_played', 'wins', 'draws', 'losses', 
                        'goals_for', 'goals_against', 'goal_difference', 'points']]

# Example usage functions
def preprocess_csv_data(file_path):
    """
    Example function to preprocess CSV data
    """
    preprocessor = FootballDataPreprocessor()
    
    # Load data
    df = pd.read_csv(file_path)
    
    # Apply preprocessing steps
    df = preprocessor.standardize_team_names(df, ['home_team', 'away_team'])
    
    if 'date' in df.columns:
        df = preprocessor.parse_match_date(df, 'date')
    
    if 'score' in df.columns:
        df = preprocessor.clean_score_data(df, 'score')
    
    df = preprocessor.create_match_features(df)
    
    return df

if __name__ == "__main__":
    print("Football Data Preprocessing Utilities")
    print("Available functions:")
    print("- standardize_team_names()")
    print("- parse_match_date()")
    print("- clean_score_data()")
    print("- handle_player_data()")
    print("- create_match_features()")
    print("- create_league_table()")
    print("- detect_outliers()")