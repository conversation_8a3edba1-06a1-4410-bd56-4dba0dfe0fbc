import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# Set up plotting style
plt.style.use('default')
sns.set_palette('husl')

print('🎨 Creating Football Data Visualizations...')

# Load data
dataframes = {}
dataframes['players'] = pd.read_csv('players.csv', encoding='latin-1')
dataframes['teams'] = pd.read_csv('teams.csv')
dataframes['leagues'] = pd.read_csv('leagues.csv')
dataframes['games'] = pd.read_csv('games.csv')
dataframes['appearances'] = pd.read_csv('appearances.csv')
dataframes['shots'] = pd.read_csv('shots.csv')
dataframes['teamstats'] = pd.read_csv('teamstats.csv')

# 1. GAMES ANALYSIS VISUALIZATIONS
games_df = dataframes['games'].copy()
games_df['date'] = pd.to_datetime(games_df['date'])
games_df['total_goals'] = games_df['homeGoals'] + games_df['awayGoals']

# Create comprehensive games analysis
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('⚽ FOOTBALL GAMES ANALYSIS', fontsize=16, fontweight='bold')

# 1.1 Goals distribution
axes[0,0].hist(games_df['total_goals'], bins=15, alpha=0.7, edgecolor='black', color='skyblue')
axes[0,0].set_title('Total Goals per Game Distribution')
axes[0,0].set_xlabel('Total Goals')
axes[0,0].set_ylabel('Number of Games')
axes[0,0].grid(True, alpha=0.3)

# 1.2 Home vs Away goals scatter
axes[0,1].scatter(games_df['homeGoals'], games_df['awayGoals'], alpha=0.6, color='coral')
axes[0,1].set_title('Home vs Away Goals')
axes[0,1].set_xlabel('Home Goals')
axes[0,1].set_ylabel('Away Goals')
axes[0,1].grid(True, alpha=0.3)

# 1.3 Home advantage analysis
home_away_data = [games_df['homeGoals'], games_df['awayGoals']]
bp = axes[0,2].boxplot(home_away_data, labels=['Home', 'Away'], patch_artist=True)
bp['boxes'][0].set_facecolor('lightgreen')
bp['boxes'][1].set_facecolor('lightcoral')
axes[0,2].set_title('Home vs Away Goals Distribution')
axes[0,2].set_ylabel('Goals')
axes[0,2].grid(True, alpha=0.3)

# 1.4 Goals over time
monthly_goals = games_df.groupby(games_df['date'].dt.to_period('M'))['total_goals'].mean()
monthly_goals.plot(ax=axes[1,0], color='purple', linewidth=2)
axes[1,0].set_title('Average Goals per Game Over Time')
axes[1,0].set_xlabel('Date')
axes[1,0].set_ylabel('Average Goals')
axes[1,0].grid(True, alpha=0.3)
axes[1,0].tick_params(axis='x', rotation=45)

# 1.5 League distribution
league_games = games_df.groupby('leagueID').size().reset_index(name='count')
league_games = league_games.merge(dataframes['leagues'], on='leagueID')
axes[1,1].bar(league_games['name'], league_games['count'], color='gold')
axes[1,1].set_title('Games per League')
axes[1,1].set_ylabel('Number of Games')
axes[1,1].tick_params(axis='x', rotation=45)
axes[1,1].grid(True, alpha=0.3)

# 1.6 Season comparison
season_stats = games_df.groupby('season').agg({
    'total_goals': 'mean',
    'gameID': 'count'
}).reset_index()

ax2 = axes[1,2].twinx()
bars = axes[1,2].bar(season_stats['season'], season_stats['gameID'], alpha=0.7, color='lightblue', label='Games')
line = ax2.plot(season_stats['season'], season_stats['total_goals'], color='red', marker='o', linewidth=2, label='Avg Goals')

axes[1,2].set_title('Games and Average Goals by Season')
axes[1,2].set_xlabel('Season')
axes[1,2].set_ylabel('Number of Games', color='blue')
ax2.set_ylabel('Average Goals per Game', color='red')
axes[1,2].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('football_games_analysis.png', dpi=300, bbox_inches='tight')
plt.show()
print('✅ Games analysis chart saved as football_games_analysis.png')

# 2. SHOTS ANALYSIS
shots_df = dataframes['shots'].copy()

fig, axes = plt.subplots(2, 2, figsize=(15, 10))
fig.suptitle('🎯 SHOTS ANALYSIS', fontsize=16, fontweight='bold')

# 2.1 Shot outcomes
shot_results = shots_df['shotResult'].value_counts()
colors = ['gold', 'lightcoral', 'lightblue', 'lightgreen', 'orange', 'purple']
axes[0,0].pie(shot_results.values, labels=shot_results.index, autopct='%1.1f%%', colors=colors[:len(shot_results)])
axes[0,0].set_title('Shot Outcomes Distribution')

# 2.2 Shot types
shot_types = shots_df['shotType'].value_counts().head(8)
axes[0,1].bar(shot_types.index, shot_types.values, color='orange')
axes[0,1].set_title('Shot Types')
axes[0,1].set_ylabel('Number of Shots')
axes[0,1].tick_params(axis='x', rotation=45)
axes[0,1].grid(True, alpha=0.3)

# 2.3 xGoal distribution
axes[1,0].hist(shots_df['xGoal'], bins=30, alpha=0.7, edgecolor='black', color='purple')
axes[1,0].set_title('xGoal Distribution')
axes[1,0].set_xlabel('xGoal Value')
axes[1,0].set_ylabel('Number of Shots')
axes[1,0].grid(True, alpha=0.3)

# 2.4 xGoal by shot type
xgoal_by_type = shots_df.groupby('shotType')['xGoal'].mean().sort_values(ascending=False).head(8)
axes[1,1].bar(xgoal_by_type.index, xgoal_by_type.values, color='red')
axes[1,1].set_title('Average xGoal by Shot Type')
axes[1,1].set_ylabel('Average xGoal')
axes[1,1].tick_params(axis='x', rotation=45)
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('football_shots_analysis.png', dpi=300, bbox_inches='tight')
plt.show()
print('✅ Shots analysis chart saved as football_shots_analysis.png')

# 3. TEAM PERFORMANCE ANALYSIS
teamstats_df = dataframes['teamstats'].copy()
teamstats_df['date'] = pd.to_datetime(teamstats_df['date'])

fig, axes = plt.subplots(2, 2, figsize=(15, 10))
fig.suptitle('📈 TEAM PERFORMANCE ANALYSIS', fontsize=16, fontweight='bold')

# 3.1 Home vs Away performance
home_away_goals = teamstats_df.groupby('location')['goals'].mean()
colors = ['lightgreen', 'lightcoral']
axes[0,0].bar(['Home', 'Away'], home_away_goals.values, color=colors)
axes[0,0].set_title('Average Goals: Home vs Away')
axes[0,0].set_ylabel('Average Goals')
axes[0,0].grid(True, alpha=0.3)

# 3.2 Goals vs xGoals
sample_team_data = teamstats_df.sample(min(2000, len(teamstats_df)))
axes[0,1].scatter(sample_team_data['xGoals'], sample_team_data['goals'], alpha=0.6, color='blue')
axes[0,1].plot([0, sample_team_data['xGoals'].max()], [0, sample_team_data['xGoals'].max()], 'r--', label='Perfect prediction')
axes[0,1].set_title('Expected vs Actual Goals (Team Level)')
axes[0,1].set_xlabel('xGoals')
axes[0,1].set_ylabel('Actual Goals')
axes[0,1].legend()
axes[0,1].grid(True, alpha=0.3)

# 3.3 Results distribution
results = teamstats_df['result'].value_counts()
result_colors = {'W': 'green', 'D': 'yellow', 'L': 'red'}
colors = [result_colors.get(result, 'gray') for result in results.index]
axes[1,0].pie(results.values, labels=results.index, autopct='%1.1f%%', colors=colors)
axes[1,0].set_title('Match Results Distribution')

# 3.4 PPDA (Passes per Defensive Action) analysis
axes[1,1].hist(teamstats_df['ppda'], bins=25, alpha=0.7, edgecolor='black', color='purple')
axes[1,1].set_title('PPDA Distribution')
axes[1,1].set_xlabel('Passes per Defensive Action')
axes[1,1].set_ylabel('Number of Games')
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('football_team_analysis.png', dpi=300, bbox_inches='tight')
plt.show()
print('✅ Team performance chart saved as football_team_analysis.png')

print('\n🎉 All visualizations created successfully!')
print('📊 Charts saved as PNG files in your directory')
