import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

print('🏈 FOOTBALL DATABASE ANALYSIS')
print('='*50)

# Load data with proper encoding
dataframes = {}

try:
    dataframes['players'] = pd.read_csv('players.csv', encoding='latin-1')
    print(f'✓ Loaded players.csv: {dataframes["players"].shape}')
except Exception as e:
    print(f'✗ Error loading players.csv: {e}')

dataframes['teams'] = pd.read_csv('teams.csv')
dataframes['leagues'] = pd.read_csv('leagues.csv')
dataframes['games'] = pd.read_csv('games.csv')
dataframes['appearances'] = pd.read_csv('appearances.csv')
dataframes['shots'] = pd.read_csv('shots.csv')
dataframes['teamstats'] = pd.read_csv('teamstats.csv')

print(f'\nTotal datasets loaded: {len(dataframes)}')

# COMPREHENSIVE FOOTBALL DATA ANALYSIS
print('\n🏈 COMPREHENSIVE FOOTBALL DATA ANALYSIS')
print('='*50)

# 1. PLAYERS ANALYSIS
if 'players' in dataframes:
    players_df = dataframes['players']
    print(f'\n📊 PLAYERS ANALYSIS')
    print(f'Total unique players: {len(players_df):,}')
    print(f'Sample players: {players_df["name"].head().tolist()}')

# 2. TEAMS ANALYSIS  
teams_df = dataframes['teams']
print(f'\n⚽ TEAMS ANALYSIS')
print(f'Total teams: {len(teams_df):,}')
print(f'Sample teams: {teams_df["name"].head().tolist()}')

# 3. LEAGUES ANALYSIS
leagues_df = dataframes['leagues']
print(f'\n🏆 LEAGUES ANALYSIS')
print(f'Total leagues: {len(leagues_df):,}')
print('Available leagues:')
for _, league in leagues_df.iterrows():
    print(f'  - {league["name"]} ({league["understatNotation"]})')

# 4. GAMES ANALYSIS
games_df = dataframes['games']
print(f'\n🎮 GAMES ANALYSIS')
print(f'Total games: {len(games_df):,}')

# Convert date column
games_df['date'] = pd.to_datetime(games_df['date'])

# Basic game statistics
print(f'Date range: {games_df["date"].min().date()} to {games_df["date"].max().date()}')
print(f'Seasons covered: {sorted(games_df["season"].unique())}')
print(f'Total goals (home): {games_df["homeGoals"].sum():,}')
print(f'Total goals (away): {games_df["awayGoals"].sum():,}')
print(f'Average goals per game: {(games_df["homeGoals"] + games_df["awayGoals"]).mean():.2f}')

# League distribution
league_games = games_df.groupby('leagueID').size().reset_index(name='game_count')
league_games = league_games.merge(leagues_df, left_on='leagueID', right_on='leagueID')
print('\nGames per league:')
for _, row in league_games.iterrows():
    print(f'  - {row["name"]}: {row["game_count"]:,} games')

# 5. APPEARANCES ANALYSIS
appearances_df = dataframes['appearances']
print(f'\n👤 PLAYER APPEARANCES ANALYSIS')
print(f'Total appearances: {len(appearances_df):,}')

# Goals and assists
total_goals = appearances_df['goals'].sum()
total_assists = appearances_df['assists'].sum()
total_shots = appearances_df['shots'].sum()

print(f'Total goals scored: {total_goals:,}')
print(f'Total assists: {total_assists:,}')
print(f'Total shots: {total_shots:,}')
print(f'Average xGoals per appearance: {appearances_df["xGoals"].mean():.3f}')

# Position analysis
position_counts = appearances_df['position'].value_counts()
print(f'\nMost common positions:')
for pos, count in position_counts.head().items():
    print(f'  - {pos}: {count:,} appearances')

# 6. SHOTS ANALYSIS
shots_df = dataframes['shots']
print(f'\n🎯 SHOTS ANALYSIS')
print(f'Total shots: {len(shots_df):,}')

# Shot outcomes
shot_results = shots_df['shotResult'].value_counts()
print('\nShot outcomes:')
for result, count in shot_results.items():
    percentage = (count / len(shots_df)) * 100
    print(f'  - {result}: {count:,} ({percentage:.1f}%)')

# Shot types
shot_types = shots_df['shotType'].value_counts()
print('\nShot types:')
for shot_type, count in shot_types.head().items():
    print(f'  - {shot_type}: {count:,}')

# Average xGoal
print(f'\nAverage xGoal per shot: {shots_df["xGoal"].mean():.3f}')

# 7. TEAM STATS ANALYSIS
teamstats_df = dataframes['teamstats']
print(f'\n📈 TEAM STATISTICS ANALYSIS')
print(f'Total team game records: {len(teamstats_df):,}')

# Convert date
teamstats_df['date'] = pd.to_datetime(teamstats_df['date'])

# Home vs Away performance
home_stats = teamstats_df[teamstats_df['location'] == 'h']
away_stats = teamstats_df[teamstats_df['location'] == 'a']

print(f'\nHome vs Away comparison:')
print(f'Home games: {len(home_stats):,}')
print(f'Away games: {len(away_stats):,}')
print(f'Average goals (home): {home_stats["goals"].mean():.2f}')
print(f'Average goals (away): {away_stats["goals"].mean():.2f}')
print(f'Average xGoals (home): {home_stats["xGoals"].mean():.2f}')
print(f'Average xGoals (away): {away_stats["xGoals"].mean():.2f}')

# Results distribution
results = teamstats_df['result'].value_counts()
print(f'\nResults distribution:')
for result, count in results.items():
    percentage = (count / len(teamstats_df)) * 100
    print(f'  - {result}: {count:,} ({percentage:.1f}%)')

print('\n✅ Basic analysis complete!')
