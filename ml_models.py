"""
Machine Learning Models for Football Data Analysis
Predictive models for match outcomes, goals, and team performance
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.svm import SVC, SVR
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (accuracy_score, classification_report, confusion_matrix,
                           mean_squared_error, mean_absolute_error, r2_score)
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

class FootballMLModels:
    """
    Machine Learning models for football data analysis
    """
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.feature_importance = {}
        
    def prepare_match_features(self, matches_df):
        """
        Prepare features for match prediction
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        """
        df = matches_df.copy()
        
        # Basic features
        feature_columns = []
        
        # Statistical features
        stat_features = ['home_shots', 'away_shots', 'home_possession', 'away_possession',
                        'home_corners', 'away_corners', 'home_fouls', 'away_fouls',
                        'home_yellow_cards', 'away_yellow_cards', 'attendance']
        
        for col in stat_features:
            if col in df.columns:
                feature_columns.append(col)
        
        # Create team strength features (if we have historical data)
        if len(df) > 50:  # Need sufficient data
            team_stats = self._calculate_team_strength(df)
            df = df.merge(team_stats[['team', 'avg_goals_scored', 'avg_goals_conceded', 'win_rate']], 
                         left_on='home_team', right_on='team', how='left', suffixes=('', '_home'))
            df = df.merge(team_stats[['team', 'avg_goals_scored', 'avg_goals_conceded', 'win_rate']], 
                         left_on='away_team', right_on='team', how='left', suffixes=('', '_away'))
            
            feature_columns.extend(['avg_goals_scored', 'avg_goals_conceded', 'win_rate',
                                  'avg_goals_scored_away', 'avg_goals_conceded_away', 'win_rate_away'])
        
        # Create derived features
        if 'home_possession' in df.columns and 'away_possession' in df.columns:
            df['possession_difference'] = df['home_possession'] - df['away_possession']
            feature_columns.append('possession_difference')
        
        if 'home_shots' in df.columns and 'away_shots' in df.columns:
            df['shots_difference'] = df['home_shots'] - df['away_shots']
            feature_columns.append('shots_difference')
        
        # Handle missing values
        for col in feature_columns:
            if col in df.columns:
                df[col] = df[col].fillna(df[col].median())
        
        return df, feature_columns
    
    def _calculate_team_strength(self, matches_df):
        """
        Calculate team strength metrics
        """
        teams = list(set(matches_df['home_team'].unique().tolist() + 
                        matches_df['away_team'].unique().tolist()))
        
        team_stats = []
        
        for team in teams:
            home_matches = matches_df[matches_df['home_team'] == team]
            away_matches = matches_df[matches_df['away_team'] == team]
            
            # Goals
            goals_scored = home_matches['home_goals'].sum() + away_matches['away_goals'].sum()
            goals_conceded = home_matches['away_goals'].sum() + away_matches['home_goals'].sum()
            total_matches = len(home_matches) + len(away_matches)
            
            # Wins
            home_wins = len(home_matches[home_matches['home_goals'] > home_matches['away_goals']])
            away_wins = len(away_matches[away_matches['away_goals'] > away_matches['home_goals']])
            total_wins = home_wins + away_wins
            
            team_stats.append({
                'team': team,
                'avg_goals_scored': goals_scored / total_matches if total_matches > 0 else 0,
                'avg_goals_conceded': goals_conceded / total_matches if total_matches > 0 else 0,
                'win_rate': total_wins / total_matches if total_matches > 0 else 0
            })
        
        return pd.DataFrame(team_stats)
    
    def predict_match_results(self, matches_df, test_size=0.2):
        """
        Build models to predict match results (Win/Draw/Loss)
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        test_size (float): Test set size
        """
        print("=== MATCH RESULT PREDICTION ===")
        
        # Prepare data
        df, feature_columns = self.prepare_match_features(matches_df)
        
        if len(feature_columns) < 3:
            print("Not enough features for prediction")
            return
        
        X = df[feature_columns].fillna(0)
        y = df['result'] if 'result' in df.columns else None
        
        if y is None:
            print("Result column not found")
            return
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=42)
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        self.scalers['result_prediction'] = scaler
        
        # Define models
        models = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'SVM': SVC(random_state=42, probability=True),
            'Naive Bayes': GaussianNB(),
            'KNN': KNeighborsClassifier(n_neighbors=5)
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"\nTraining {name}...")
            
            if name in ['Logistic Regression', 'SVM', 'KNN']:
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
                y_pred_proba = model.predict_proba(X_test_scaled) if hasattr(model, 'predict_proba') else None
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                y_pred_proba = model.predict_proba(X_test) if hasattr(model, 'predict_proba') else None
            
            accuracy = accuracy_score(y_test, y_pred)
            
            results[name] = {
                'model': model,
                'accuracy': accuracy,
                'predictions': y_pred,
                'probabilities': y_pred_proba
            }
            
            print(f"{name} Accuracy: {accuracy:.3f}")
            
            # Feature importance for tree-based models
            if hasattr(model, 'feature_importances_'):
                importance_df = pd.DataFrame({
                    'feature': feature_columns,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)
                
                self.feature_importance[name] = importance_df
                print(f"Top 5 features for {name}:")
                print(importance_df.head())
        
        # Find best model
        best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
        best_model = results[best_model_name]
        
        print(f"\nBest Model: {best_model_name} (Accuracy: {best_model['accuracy']:.3f})")
        
        # Detailed evaluation of best model
        print(f"\nDetailed Classification Report for {best_model_name}:")
        print(classification_report(y_test, best_model['predictions']))
        
        # Confusion Matrix
        cm = confusion_matrix(y_test, best_model['predictions'])
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['Away Win', 'Draw', 'Home Win'],
                   yticklabels=['Away Win', 'Draw', 'Home Win'])
        plt.title(f'Confusion Matrix - {best_model_name}')
        plt.ylabel('Actual')
        plt.xlabel('Predicted')
        plt.show()
        
        self.models['result_prediction'] = results
        return results
    
    def predict_total_goals(self, matches_df, test_size=0.2):
        """
        Build models to predict total goals in a match
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        test_size (float): Test set size
        """
        print("\n=== TOTAL GOALS PREDICTION ===")
        
        # Prepare data
        df, feature_columns = self.prepare_match_features(matches_df)
        
        if 'total_goals' not in df.columns:
            df['total_goals'] = df['home_goals'] + df['away_goals']
        
        X = df[feature_columns].fillna(0)
        y = df['total_goals']
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=42)
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        self.scalers['goals_prediction'] = scaler
        
        # Define models
        models = {
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'Linear Regression': LinearRegression(),
            'SVR': SVR(kernel='rbf'),
            'Gradient Boosting': GradientBoostingClassifier(random_state=42)
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"\nTraining {name}...")
            
            if name in ['Linear Regression', 'SVR']:
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
            
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            results[name] = {
                'model': model,
                'mse': mse,
                'mae': mae,
                'r2': r2,
                'predictions': y_pred
            }
            
            print(f"{name} - MSE: {mse:.3f}, MAE: {mae:.3f}, R²: {r2:.3f}")
        
        # Find best model (lowest MAE)
        best_model_name = min(results.keys(), key=lambda x: results[x]['mae'])
        best_model = results[best_model_name]
        
        print(f"\nBest Model: {best_model_name} (MAE: {best_model['mae']:.3f})")
        
        # Visualization
        plt.figure(figsize=(10, 6))
        plt.scatter(y_test, best_model['predictions'], alpha=0.6)
        plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
        plt.xlabel('Actual Goals')
        plt.ylabel('Predicted Goals')
        plt.title(f'Actual vs Predicted Goals - {best_model_name}')
        plt.show()
        
        self.models['goals_prediction'] = results
        return results
    
    def predict_team_performance(self, matches_df, target_metric='points'):
        """
        Predict team performance metrics
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        target_metric (str): Metric to predict ('points', 'goals_scored', etc.)
        """
        print(f"\n=== TEAM {target_metric.upper()} PREDICTION ===")
        
        # Create team-level features
        team_features = self._create_team_features(matches_df)
        
        if target_metric not in team_features.columns:
            print(f"Target metric '{target_metric}' not found")
            return
        
        # Prepare features
        feature_cols = [col for col in team_features.columns 
                       if col not in ['team', target_metric] and 
                       team_features[col].dtype in ['int64', 'float64']]
        
        X = team_features[feature_cols].fillna(0)
        y = team_features[target_metric]
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        
        # Train model
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)
        
        y_pred = model.predict(X_test)
        
        # Evaluate
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        print(f"Team {target_metric} Prediction - MSE: {mse:.3f}, MAE: {mae:.3f}, R²: {r2:.3f}")
        
        # Feature importance
        importance_df = pd.DataFrame({
            'feature': feature_cols,
            'importance': model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\nTop features for predicting {target_metric}:")
        print(importance_df.head(10))
        
        return model, importance_df
    
    def _create_team_features(self, matches_df):
        """
        Create team-level features for analysis
        """
        teams = list(set(matches_df['home_team'].unique().tolist() + 
                        matches_df['away_team'].unique().tolist()))
        
        team_features = []
        
        for team in teams:
            home_matches = matches_df[matches_df['home_team'] == team]
            away_matches = matches_df[matches_df['away_team'] == team]
            
            # Basic stats
            total_matches = len(home_matches) + len(away_matches)
            
            # Goals
            goals_scored = home_matches['home_goals'].sum() + away_matches['away_goals'].sum()
            goals_conceded = home_matches['away_goals'].sum() + away_matches['home_goals'].sum()
            
            # Results
            home_wins = len(home_matches[home_matches['home_goals'] > home_matches['away_goals']])
            away_wins = len(away_matches[away_matches['away_goals'] > away_matches['home_goals']])
            home_draws = len(home_matches[home_matches['home_goals'] == home_matches['away_goals']])
            away_draws = len(away_matches[away_matches['away_goals'] == away_matches['home_goals']])
            
            total_wins = home_wins + away_wins
            total_draws = home_draws + away_draws
            total_losses = total_matches - total_wins - total_draws
            points = (total_wins * 3) + total_draws
            
            # Advanced stats
            avg_possession = 0
            avg_shots = 0
            avg_fouls = 0
            
            if 'home_possession' in matches_df.columns:
                home_poss = home_matches['home_possession'].mean()
                away_poss = away_matches['away_possession'].mean()
                avg_possession = np.nanmean([home_poss, away_poss])
            
            if 'home_shots' in matches_df.columns:
                home_shots = home_matches['home_shots'].mean()
                away_shots = away_matches['away_shots'].mean()
                avg_shots = np.nanmean([home_shots, away_shots])
            
            if 'home_fouls' in matches_df.columns:
                home_fouls = home_matches['home_fouls'].mean()
                away_fouls = away_matches['away_fouls'].mean()
                avg_fouls = np.nanmean([home_fouls, away_fouls])
            
            team_features.append({
                'team': team,
                'matches_played': total_matches,
                'wins': total_wins,
                'draws': total_draws,
                'losses': total_losses,
                'points': points,
                'goals_scored': goals_scored,
                'goals_conceded': goals_conceded,
                'goal_difference': goals_scored - goals_conceded,
                'win_rate': total_wins / total_matches if total_matches > 0 else 0,
                'avg_goals_scored': goals_scored / total_matches if total_matches > 0 else 0,
                'avg_goals_conceded': goals_conceded / total_matches if total_matches > 0 else 0,
                'avg_possession': avg_possession,
                'avg_shots': avg_shots,
                'avg_fouls': avg_fouls,
                'home_advantage': home_wins / len(home_matches) if len(home_matches) > 0 else 0
            })
        
        return pd.DataFrame(team_features)
    
    def cross_validate_models(self, matches_df, cv_folds=5):
        """
        Perform cross-validation on models
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        cv_folds (int): Number of cross-validation folds
        """
        print(f"\n=== CROSS-VALIDATION ({cv_folds} folds) ===")
        
        # Prepare data
        df, feature_columns = self.prepare_match_features(matches_df)
        X = df[feature_columns].fillna(0)
        y = df['result'] if 'result' in df.columns else None
        
        if y is None:
            print("Result column not found")
            return
        
        # Models to test
        models = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'SVM': SVC(random_state=42),
            'Naive Bayes': GaussianNB()
        }
        
        cv_results = {}
        
        for name, model in models.items():
            print(f"\nCross-validating {name}...")
            
            if name in ['Logistic Regression', 'SVM']:
                # Scale features for these models
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)
                scores = cross_val_score(model, X_scaled, y, cv=cv_folds, scoring='accuracy')
            else:
                scores = cross_val_score(model, X, y, cv=cv_folds, scoring='accuracy')
            
            cv_results[name] = {
                'mean_score': scores.mean(),
                'std_score': scores.std(),
                'scores': scores
            }
            
            print(f"{name} - Mean Accuracy: {scores.mean():.3f} (+/- {scores.std() * 2:.3f})")
        
        return cv_results
    
    def hyperparameter_tuning(self, matches_df, model_name='Random Forest'):
        """
        Perform hyperparameter tuning for a specific model
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        model_name (str): Name of the model to tune
        """
        print(f"\n=== HYPERPARAMETER TUNING - {model_name} ===")
        
        # Prepare data
        df, feature_columns = self.prepare_match_features(matches_df)
        X = df[feature_columns].fillna(0)
        y = df['result'] if 'result' in df.columns else None
        
        if y is None:
            print("Result column not found")
            return
        
        # Define parameter grids
        param_grids = {
            'Random Forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [None, 10, 20],
                'min_samples_split': [2, 5, 10]
            },
            'Logistic Regression': {
                'C': [0.1, 1, 10],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear']
            },
            'SVM': {
                'C': [0.1, 1, 10],
                'kernel': ['rbf', 'linear'],
                'gamma': ['scale', 'auto']
            }
        }
        
        if model_name not in param_grids:
            print(f"Hyperparameter tuning not available for {model_name}")
            return
        
        # Define model
        models = {
            'Random Forest': RandomForestClassifier(random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'SVM': SVC(random_state=42)
        }
        
        model = models[model_name]
        param_grid = param_grids[model_name]
        
        # Perform grid search
        grid_search = GridSearchCV(model, param_grid, cv=5, scoring='accuracy', n_jobs=-1)
        
        if model_name in ['Logistic Regression', 'SVM']:
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            grid_search.fit(X_scaled, y)
        else:
            grid_search.fit(X, y)
        
        print(f"Best parameters: {grid_search.best_params_}")
        print(f"Best cross-validation score: {grid_search.best_score_:.3f}")
        
        return grid_search.best_estimator_, grid_search.best_params_

# Example usage
def run_ml_analysis():
    """
    Run complete ML analysis with sample data
    """
    from football_analysis import FootballDataAnalyzer
    
    # Create sample data
    analyzer = FootballDataAnalyzer()
    matches_df = analyzer.create_sample_data()
    
    # Initialize ML models
    ml_models = FootballMLModels()
    
    # Run predictions
    ml_models.predict_match_results(matches_df)
    ml_models.predict_total_goals(matches_df)
    ml_models.cross_validate_models(matches_df)

if __name__ == "__main__":
    print("Football Machine Learning Models")
    print("Available functions:")
    print("- predict_match_results()")
    print("- predict_total_goals()")
    print("- predict_team_performance()")
    print("- cross_validate_models()")
    print("- hyperparameter_tuning()")
    print("\nTo run complete analysis: run_ml_analysis()")