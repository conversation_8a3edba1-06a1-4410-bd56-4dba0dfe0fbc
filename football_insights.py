import pandas as pd
import numpy as np

print('🏆 ADVANCED FOOTBALL INSIGHTS & TOP PERFORMERS')
print('='*60)

# Load data
dataframes = {}
dataframes['players'] = pd.read_csv('players.csv', encoding='latin-1')
dataframes['teams'] = pd.read_csv('teams.csv')
dataframes['leagues'] = pd.read_csv('leagues.csv')
dataframes['games'] = pd.read_csv('games.csv')
dataframes['appearances'] = pd.read_csv('appearances.csv')
dataframes['shots'] = pd.read_csv('shots.csv')
dataframes['teamstats'] = pd.read_csv('teamstats.csv')

# TOP PERFORMERS ANALYSIS
appearances_df = dataframes['appearances']
players_df = dataframes['players']

# Top goal scorers
top_scorers = appearances_df.groupby('playerID')['goals'].sum().sort_values(ascending=False).head(10)
top_scorers_with_names = top_scorers.reset_index().merge(players_df, on='playerID')

print('\n🥅 TOP 10 GOAL SCORERS:')
for i, (_, player) in enumerate(top_scorers_with_names.iterrows(), 1):
    print(f'  {i:2}. {player["name"]:25} - {player["goals"]:3} goals')

# Top assisters
top_assisters = appearances_df.groupby('playerID')['assists'].sum().sort_values(ascending=False).head(10)
top_assisters_with_names = top_assisters.reset_index().merge(players_df, on='playerID')

print('\n🎯 TOP 10 ASSIST PROVIDERS:')
for i, (_, player) in enumerate(top_assisters_with_names.iterrows(), 1):
    print(f'  {i:2}. {player["name"]:25} - {player["assists"]:3} assists')

# Most shots taken
top_shooters = appearances_df.groupby('playerID')['shots'].sum().sort_values(ascending=False).head(10)
top_shooters_with_names = top_shooters.reset_index().merge(players_df, on='playerID')

print('\n🎯 TOP 10 SHOT TAKERS:')
for i, (_, player) in enumerate(top_shooters_with_names.iterrows(), 1):
    print(f'  {i:2}. {player["name"]:25} - {player["shots"]:4} shots')

# Best xGoals performers
top_xgoals = appearances_df.groupby('playerID')['xGoals'].sum().sort_values(ascending=False).head(10)
top_xgoals_with_names = top_xgoals.reset_index().merge(players_df, on='playerID')

print('\n📊 TOP 10 xGOALS PERFORMERS:')
for i, (_, player) in enumerate(top_xgoals_with_names.iterrows(), 1):
    print(f'  {i:2}. {player["name"]:25} - {player["xGoals"]:5.2f} xGoals')

# TEAM PERFORMANCE ANALYSIS
teamstats_df = dataframes['teamstats']
teams_df = dataframes['teams']

# Best attacking teams
team_goals = teamstats_df.groupby('teamID')['goals'].mean().sort_values(ascending=False).head(10)
team_goals_with_names = team_goals.reset_index().merge(teams_df, on='teamID')

print('\n⚽ TOP 10 ATTACKING TEAMS (Average Goals per Game):')
for i, (_, team) in enumerate(team_goals_with_names.iterrows(), 1):
    print(f'  {i:2}. {team["name"]:25} - {team["goals"]:4.2f} goals/game')

# Best xGoals teams
team_xgoals = teamstats_df.groupby('teamID')['xGoals'].mean().sort_values(ascending=False).head(10)
team_xgoals_with_names = team_xgoals.reset_index().merge(teams_df, on='teamID')

print('\n📈 TOP 10 xGOALS TEAMS (Expected Goals per Game):')
for i, (_, team) in enumerate(team_xgoals_with_names.iterrows(), 1):
    print(f'  {i:2}. {team["name"]:25} - {team["xGoals"]:4.2f} xGoals/game')

# Most efficient teams (goals vs xGoals)
team_efficiency = teamstats_df.groupby('teamID').agg({
    'goals': 'sum',
    'xGoals': 'sum'
}).reset_index()
team_efficiency['efficiency'] = team_efficiency['goals'] / team_efficiency['xGoals']
team_efficiency = team_efficiency.sort_values('efficiency', ascending=False).head(10)
team_efficiency_with_names = team_efficiency.merge(teams_df, on='teamID')

print('\n🎯 TOP 10 MOST EFFICIENT TEAMS (Goals/xGoals Ratio):')
for i, (_, team) in enumerate(team_efficiency_with_names.iterrows(), 1):
    print(f'  {i:2}. {team["name"]:25} - {team["efficiency"]:4.2f} efficiency')

# LEAGUE COMPARISON
games_df = dataframes['games']
leagues_df = dataframes['leagues']

league_stats = games_df.groupby('leagueID').agg({
    'homeGoals': 'mean',
    'awayGoals': 'mean'
}).reset_index()
league_stats['total_goals'] = league_stats['homeGoals'] + league_stats['awayGoals']
league_stats['home_advantage'] = league_stats['homeGoals'] - league_stats['awayGoals']
league_stats = league_stats.merge(leagues_df, on='leagueID')

print('\n🏆 LEAGUE COMPARISON:')
for _, league in league_stats.iterrows():
    print(f'\n  {league["name"]}:')
    print(f'    Average goals/game: {league["total_goals"]:.2f}')
    print(f'    Home advantage: +{league["home_advantage"]:.2f} goals')
    print(f'    Home goals: {league["homeGoals"]:.2f} | Away goals: {league["awayGoals"]:.2f}')

# SHOT ANALYSIS INSIGHTS
shots_df = dataframes['shots']

print('\n🎯 SHOT ANALYSIS INSIGHTS:')
print(f'Total shots analyzed: {len(shots_df):,}')

# Conversion rates by shot type
shot_conversion = shots_df.groupby('shotType').agg({
    'shotResult': lambda x: (x == 'Goal').sum(),
    'xGoal': 'count'
}).reset_index()
shot_conversion['conversion_rate'] = (shot_conversion['shotResult'] / shot_conversion['xGoal']) * 100
shot_conversion = shot_conversion.sort_values('conversion_rate', ascending=False)

print('\nConversion rates by shot type:')
for _, shot in shot_conversion.iterrows():
    print(f'  {shot["shotType"]:15} - {shot["conversion_rate"]:5.1f}% ({shot["shotResult"]:,} goals from {shot["xGoal"]:,} shots)')

# Best shot situations
situation_conversion = shots_df.groupby('situation').agg({
    'shotResult': lambda x: (x == 'Goal').sum(),
    'xGoal': 'count'
}).reset_index()
situation_conversion['conversion_rate'] = (situation_conversion['shotResult'] / situation_conversion['xGoal']) * 100
situation_conversion = situation_conversion.sort_values('conversion_rate', ascending=False).head(5)

print('\nBest shot situations:')
for _, situation in situation_conversion.iterrows():
    print(f'  {situation["situation"]:15} - {situation["conversion_rate"]:5.1f}% conversion rate')

print('\n' + '='*60)
print('🎉 FOOTBALL DATA ANALYSIS COMPLETE!')
print('📊 Check the generated PNG files for detailed visualizations')
print('📓 Open football_analysis.ipynb in Jupyter for interactive analysis')
print('='*60)
