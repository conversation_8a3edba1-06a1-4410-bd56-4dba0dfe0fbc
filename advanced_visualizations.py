"""
Advanced Visualization Tools for Football Data Analysis
Interactive and sophisticated plotting functions
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

class FootballVisualizer:
    """
    Advanced visualization class for football data
    """
    
    def __init__(self):
        # Set default styling
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    def create_team_performance_radar(self, team_stats_df, team_name, metrics=None):
        """
        Create a radar chart for team performance
        
        Parameters:
        team_stats_df (DataFrame): Team statistics dataframe
        team_name (str): Name of the team to visualize
        metrics (list): List of metrics to include in radar chart
        """
        if metrics is None:
            metrics = ['goals_scored', 'goals_conceded', 'wins', 'draws', 'points']
        
        team_data = team_stats_df[team_stats_df['team'] == team_name]
        
        if team_data.empty:
            print(f"No data found for team: {team_name}")
            return
        
        # Normalize metrics to 0-100 scale
        normalized_values = []
        for metric in metrics:
            if metric in team_data.columns:
                max_val = team_stats_df[metric].max()
                min_val = team_stats_df[metric].min()
                if max_val != min_val:
                    normalized = ((team_data[metric].iloc[0] - min_val) / (max_val - min_val)) * 100
                else:
                    normalized = 50
                normalized_values.append(normalized)
            else:
                normalized_values.append(0)
        
        # Create radar chart
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=normalized_values + [normalized_values[0]],  # Close the polygon
            theta=metrics + [metrics[0]],
            fill='toself',
            name=team_name,
            line_color='rgb(32, 201, 151)'
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )),
            showlegend=True,
            title=f"Performance Radar Chart - {team_name}"
        )
        
        fig.show()
        return fig
    
    def create_goals_timeline(self, matches_df, team_name=None):
        """
        Create a timeline of goals scored/conceded
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        team_name (str): Specific team to analyze (optional)
        """
        if 'date' not in matches_df.columns:
            print("Date column required for timeline visualization")
            return
        
        df = matches_df.copy()
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        if team_name:
            # Filter for specific team
            team_matches = df[(df['home_team'] == team_name) | (df['away_team'] == team_name)].copy()
            
            # Calculate goals for/against for the team
            team_matches['goals_for'] = np.where(
                team_matches['home_team'] == team_name,
                team_matches['home_goals'],
                team_matches['away_goals']
            )
            team_matches['goals_against'] = np.where(
                team_matches['home_team'] == team_name,
                team_matches['away_goals'],
                team_matches['home_goals']
            )
            
            # Create cumulative sums
            team_matches['cumulative_goals_for'] = team_matches['goals_for'].cumsum()
            team_matches['cumulative_goals_against'] = team_matches['goals_against'].cumsum()
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=team_matches['date'],
                y=team_matches['cumulative_goals_for'],
                mode='lines+markers',
                name='Goals For',
                line=dict(color='green', width=3)
            ))
            
            fig.add_trace(go.Scatter(
                x=team_matches['date'],
                y=team_matches['cumulative_goals_against'],
                mode='lines+markers',
                name='Goals Against',
                line=dict(color='red', width=3)
            ))
            
            fig.update_layout(
                title=f'Goals Timeline - {team_name}',
                xaxis_title='Date',
                yaxis_title='Cumulative Goals',
                hovermode='x unified'
            )
        else:
            # Overall league goals timeline
            df['cumulative_total_goals'] = (df['home_goals'] + df['away_goals']).cumsum()
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=df['date'],
                y=df['cumulative_total_goals'],
                mode='lines+markers',
                name='Total Goals',
                line=dict(color='blue', width=3)
            ))
            
            fig.update_layout(
                title='League Goals Timeline',
                xaxis_title='Date',
                yaxis_title='Cumulative Total Goals',
                hovermode='x unified'
            )
        
        fig.show()
        return fig
    
    def create_heatmap_results(self, matches_df):
        """
        Create a heatmap of head-to-head results
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        """
        # Create results matrix
        teams = sorted(list(set(matches_df['home_team'].unique().tolist() + 
                               matches_df['away_team'].unique().tolist())))
        
        results_matrix = pd.DataFrame(0, index=teams, columns=teams)
        
        for _, match in matches_df.iterrows():
            home_team = match['home_team']
            away_team = match['away_team']
            
            if match['home_goals'] > match['away_goals']:
                results_matrix.loc[home_team, away_team] += 3  # Home win
            elif match['home_goals'] < match['away_goals']:
                results_matrix.loc[away_team, home_team] += 3  # Away win
            else:
                results_matrix.loc[home_team, away_team] += 1  # Draw
                results_matrix.loc[away_team, home_team] += 1  # Draw
        
        # Create heatmap
        plt.figure(figsize=(12, 10))
        sns.heatmap(results_matrix, annot=True, cmap='RdYlGn', center=1.5,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        plt.title('Head-to-Head Results Matrix (Points Gained)')
        plt.xlabel('Opponent')
        plt.ylabel('Team')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.show()
        
        return results_matrix
    
    def create_interactive_league_table(self, team_stats_df):
        """
        Create an interactive league table
        
        Parameters:
        team_stats_df (DataFrame): Team statistics dataframe
        """
        fig = go.Figure(data=[go.Table(
            header=dict(values=list(team_stats_df.columns),
                       fill_color='paleturquoise',
                       align='left',
                       font=dict(size=12, color='black')),
            cells=dict(values=[team_stats_df[col] for col in team_stats_df.columns],
                      fill_color='lavender',
                      align='left',
                      font=dict(size=11)))
        ])
        
        fig.update_layout(
            title="Interactive League Table",
            height=600
        )
        
        fig.show()
        return fig
    
    def create_goal_distribution_analysis(self, matches_df):
        """
        Create comprehensive goal distribution analysis
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        """
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Goals per Match Distribution', 'Home vs Away Goals',
                          'Goal Difference Distribution', 'Goals by Time Period'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 1. Goals per match distribution
        total_goals = matches_df['home_goals'] + matches_df['away_goals']
        fig.add_trace(
            go.Histogram(x=total_goals, name='Goals per Match', nbinsx=15),
            row=1, col=1
        )
        
        # 2. Home vs Away goals scatter
        fig.add_trace(
            go.Scatter(x=matches_df['home_goals'], y=matches_df['away_goals'],
                      mode='markers', name='Home vs Away Goals',
                      opacity=0.6),
            row=1, col=2
        )
        
        # 3. Goal difference distribution
        goal_diff = matches_df['home_goals'] - matches_df['away_goals']
        fig.add_trace(
            go.Histogram(x=goal_diff, name='Goal Difference', nbinsx=15),
            row=2, col=1
        )
        
        # 4. Goals by matchday (if available)
        if 'matchday' in matches_df.columns:
            matchday_goals = matches_df.groupby('matchday')['total_goals'].mean().reset_index()
            fig.add_trace(
                go.Scatter(x=matchday_goals['matchday'], y=matchday_goals['total_goals'],
                          mode='lines+markers', name='Avg Goals by Matchday'),
                row=2, col=2
            )
        
        fig.update_layout(height=800, showlegend=True, 
                         title_text="Goal Distribution Analysis")
        fig.show()
        return fig
    
    def create_team_comparison_chart(self, team_stats_df, teams_to_compare, metrics):
        """
        Create a comparison chart for multiple teams
        
        Parameters:
        team_stats_df (DataFrame): Team statistics dataframe
        teams_to_compare (list): List of team names to compare
        metrics (list): List of metrics to compare
        """
        comparison_data = team_stats_df[team_stats_df['team'].isin(teams_to_compare)]
        
        fig = go.Figure()
        
        for metric in metrics:
            if metric in comparison_data.columns:
                fig.add_trace(go.Bar(
                    name=metric,
                    x=comparison_data['team'],
                    y=comparison_data[metric],
                    text=comparison_data[metric],
                    textposition='auto'
                ))
        
        fig.update_layout(
            title=f'Team Comparison: {", ".join(teams_to_compare)}',
            xaxis_title='Team',
            yaxis_title='Value',
            barmode='group',
            height=600
        )
        
        fig.show()
        return fig
    
    def create_performance_trends(self, matches_df, team_name, window=5):
        """
        Create performance trends for a specific team
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        team_name (str): Team name
        window (int): Rolling window size
        """
        # Filter team matches
        team_matches = matches_df[
            (matches_df['home_team'] == team_name) | 
            (matches_df['away_team'] == team_name)
        ].copy()
        
        if 'date' in team_matches.columns:
            team_matches = team_matches.sort_values('date')
        
        # Calculate team-specific metrics
        team_matches['points'] = 0
        team_matches['goals_for'] = 0
        team_matches['goals_against'] = 0
        
        for idx, match in team_matches.iterrows():
            if match['home_team'] == team_name:
                goals_for = match['home_goals']
                goals_against = match['away_goals']
                if match['home_goals'] > match['away_goals']:
                    points = 3
                elif match['home_goals'] == match['away_goals']:
                    points = 1
                else:
                    points = 0
            else:
                goals_for = match['away_goals']
                goals_against = match['home_goals']
                if match['away_goals'] > match['home_goals']:
                    points = 3
                elif match['away_goals'] == match['home_goals']:
                    points = 1
                else:
                    points = 0
            
            team_matches.loc[idx, 'points'] = points
            team_matches.loc[idx, 'goals_for'] = goals_for
            team_matches.loc[idx, 'goals_against'] = goals_against
        
        # Calculate rolling averages
        team_matches['rolling_points'] = team_matches['points'].rolling(window=window).mean()
        team_matches['rolling_goals_for'] = team_matches['goals_for'].rolling(window=window).mean()
        team_matches['rolling_goals_against'] = team_matches['goals_against'].rolling(window=window).mean()
        
        # Create the plot
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=(f'{team_name} - Points Trend', f'{team_name} - Goals Trend'),
            vertical_spacing=0.1
        )
        
        # Points trend
        fig.add_trace(
            go.Scatter(x=list(range(len(team_matches))), y=team_matches['rolling_points'],
                      mode='lines+markers', name=f'Rolling Points (window={window})',
                      line=dict(color='blue', width=3)),
            row=1, col=1
        )
        
        # Goals trend
        fig.add_trace(
            go.Scatter(x=list(range(len(team_matches))), y=team_matches['rolling_goals_for'],
                      mode='lines+markers', name='Goals For',
                      line=dict(color='green', width=2)),
            row=2, col=1
        )
        
        fig.add_trace(
            go.Scatter(x=list(range(len(team_matches))), y=team_matches['rolling_goals_against'],
                      mode='lines+markers', name='Goals Against',
                      line=dict(color='red', width=2)),
            row=2, col=1
        )
        
        fig.update_layout(height=800, title_text=f"Performance Trends - {team_name}")
        fig.update_xaxes(title_text="Match Number")
        fig.update_yaxes(title_text="Points per Game", row=1, col=1)
        fig.update_yaxes(title_text="Goals per Game", row=2, col=1)
        
        fig.show()
        return fig
    
    def create_dashboard(self, matches_df, team_stats_df):
        """
        Create a comprehensive dashboard
        
        Parameters:
        matches_df (DataFrame): Matches dataframe
        team_stats_df (DataFrame): Team statistics dataframe
        """
        print("Creating Football Analysis Dashboard...")
        
        # Create multiple visualizations
        self.create_goal_distribution_analysis(matches_df)
        self.create_heatmap_results(matches_df)
        self.create_interactive_league_table(team_stats_df)
        
        # If we have enough teams, create comparison
        if len(team_stats_df) >= 3:
            top_teams = team_stats_df.head(3)['team'].tolist()
            self.create_team_comparison_chart(
                team_stats_df, 
                top_teams, 
                ['points', 'goals_scored', 'goals_conceded', 'wins']
            )
        
        print("Dashboard creation complete!")

# Example usage functions
def visualize_sample_data():
    """
    Create visualizations with sample data
    """
    from football_analysis import FootballDataAnalyzer
    
    # Create sample data
    analyzer = FootballDataAnalyzer()
    matches_df = analyzer.create_sample_data()
    
    # Create team statistics
    from data_preprocessing import FootballDataPreprocessor
    preprocessor = FootballDataPreprocessor()
    team_stats_df = preprocessor.create_league_table(matches_df)
    
    # Create visualizations
    visualizer = FootballVisualizer()
    visualizer.create_dashboard(matches_df, team_stats_df)

if __name__ == "__main__":
    print("Advanced Football Visualization Tools")
    print("Available visualization functions:")
    print("- create_team_performance_radar()")
    print("- create_goals_timeline()")
    print("- create_heatmap_results()")
    print("- create_interactive_league_table()")
    print("- create_goal_distribution_analysis()")
    print("- create_team_comparison_chart()")
    print("- create_performance_trends()")
    print("- create_dashboard()")
    print("\nTo run with sample data: visualize_sample_data()")